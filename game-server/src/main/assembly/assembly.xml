<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3 http://maven.apache.org/xsd/assembly-1.1.3.xsd">
<id>assembly</id>
<formats>
    <format>tar.gz</format>
</formats>
<includeBaseDirectory>true</includeBaseDirectory>
<fileSets>
	<fileSet>
        <directory>src/main/assembly/bin/</directory>
        <outputDirectory>bin</outputDirectory>
        <fileMode>0755</fileMode>
    </fileSet>
    <fileSet>
        <directory>${basedir}/gameconf</directory>
        <outputDirectory>gameconf</outputDirectory>
    </fileSet>
    <fileSet>
        <directory>${basedir}/src/main/resources/${profiles.active}</directory>
        <includes>
            <include>*.properties</include>
            <include>*.xml</include>
        </includes>
        <filtered>true</filtered>
        <outputDirectory>${file.separator}config</outputDirectory>
    </fileSet>
	<fileSet>
        <directory>${basedir}/src/main/resources/gameconf</directory>
        <includes>
            <include>*.json</include>
        </includes>
        <filtered>true</filtered>
        <outputDirectory>${file.separator}gameconf</outputDirectory>
    </fileSet>

    <fileSet>
        <directory>${project.build.directory}/lib</directory>
        <outputDirectory>${file.separator}lib</outputDirectory>
        <includes>
            <include>*.jar</include>
        </includes>
    </fileSet>
    <fileSet>
        <directory>${basedir}/META-INF</directory>
        <includes>
            <include>*.so</include>
        </includes>
        <filtered>true</filtered>
        <outputDirectory>${file.separator}lib</outputDirectory>
    </fileSet>
	<fileSet>
        <directory>${project.build.directory}</directory>
        <outputDirectory>${file.separator}</outputDirectory>
        <includes>
            <include>*.jar</include>
        </includes>
    </fileSet>
</fileSets>
</assembly>