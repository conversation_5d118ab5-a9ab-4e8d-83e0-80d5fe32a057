#!/bin/bash
cd `dirname $0`
COMMAND=$1
if [ -z "$COMMAND" ]; then
    echo "ERROR: Please input argument: [start, stop, restart, debug]"
    exit
fi
ENV=$2
PORT=$3
case $COMMAND in
"start")
    ./start.sh $ENV $PORT
    ;;
"stop")
    ./stop.sh $PORT
    ;;
"restart")
    ./restart.sh $ENV $PORT
;;
"dump")
    ./dump.sh 
;;
"debug")
    ./dump.sh $ENV $PORT "debug"
;;
*)
  echo "command error"
  exit 1
;;
esac
