package com.dxx.game.modules.item.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.Item;
import com.dxx.game.dto.CommonProto.ItemDto;
import com.dxx.game.dto.ItemProto;
import com.dxx.game.modules.reward.model.Reward;

import java.util.List;

public interface ItemService {


    Result<ItemProto.ItemUseResponse> useItemAction(ItemProto.ItemUseRequest params);

    /**
     * 新增道具
     * @param userId
     * @param configId
     * @param count
     * @return
     */
    Item createItem(long userId, int configId, int count);

    /**
     * 获取道具列表
     * @param userId
     * @return
     */
    List<ItemDto> getAllItems(long userId);

    List<Reward> randGiftReward(int itemId, int count);
}
