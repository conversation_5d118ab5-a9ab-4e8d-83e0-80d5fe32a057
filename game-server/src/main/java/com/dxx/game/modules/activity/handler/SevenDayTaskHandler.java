package com.dxx.game.modules.activity.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.SevenDayTaskProto.*;
import com.dxx.game.modules.activity.service.SevenDayTaskService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/5/17 5:34 下午
 */
@ApiHandler
public class SevenDayTaskHandler {

    @Autowired
    private SevenDayTaskService sevenDayTaskService;

    @ApiMethod(command = MsgReqCommand.SevenDayTaskGetInfoRequest, name = "新手七日任务活动-获取数据")
    public Result<SevenDayTaskGetInfoResponse> getInfo(Message msg) {
        SevenDayTaskGetInfoRequest params = (SevenDayTaskGetInfoRequest)msg;
        return sevenDayTaskService.getInfoAction(params);

//        return Result.Success(SevenDayTaskGetInfoResponse.newBuilder().build());
    }

    @ApiMethod(command = MsgReqCommand.SevenDayTaskRewardRequest, name = "新手七日任务活动-领取任务奖励")
    public Result<SevenDayTaskRewardResponse> taskReward(Message msg) {
        SevenDayTaskRewardRequest params = (SevenDayTaskRewardRequest)msg;
        return sevenDayTaskService.taskRewardAction(params);
    }

    @ApiMethod(command = MsgReqCommand.SevenDayTaskActiveRewardRequest, name = "新手七日任务活动-领取活跃度奖励")
    public Result<SevenDayTaskActiveRewardResponse> activeReward(Message msg) {
        SevenDayTaskActiveRewardRequest params = (SevenDayTaskActiveRewardRequest)msg;
        return sevenDayTaskService.activeRewardAction(params);
    }
}
