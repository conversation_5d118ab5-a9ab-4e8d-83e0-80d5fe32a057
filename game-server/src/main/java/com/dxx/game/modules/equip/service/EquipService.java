package com.dxx.game.modules.equip.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.Equip;
import com.dxx.game.dto.CommonProto.EquipmentDto;
import com.dxx.game.dto.EquipProto;
import com.dxx.game.dto.EquipProto.EquipComposeRequest;
import com.dxx.game.dto.EquipProto.EquipComposeResponse;
import com.dxx.game.dto.EquipProto.EquipDressRequest;
import com.dxx.game.dto.EquipProto.EquipDressResponse;

import java.util.List;

public interface EquipService {

    /**
     * 装备合成
     */
    Result<EquipComposeResponse> equipCompose(EquipComposeRequest params);

    /**
     * 装备穿戴
     */
    Result<EquipDressResponse> equipDress(EquipDressRequest params);

    //--------------------------------------------------------------------------------------------

    /**
     * 新增装备
     */
    Equip createEquip(long userId, int configId, int level);

    List<Equip> getAllEquip(long userId);

    List<EquipmentDto> getAllEquipDto(long userId, List<Equip> equips);

    List<Long> getAllEquipIds(List<Equip> equips);
    /**
     * 获取所有装备列表
     */
    List<EquipmentDto> getAllEquips(long userId);

    List<EquipmentDto> getAllEquips(List<Equip> equips);

    Result<EquipProto.EquipOffResponse> equipOff(EquipProto.EquipOffRequest params);

    Result<EquipProto.EquipQualityDownResponse> qualityDown(EquipProto.EquipQualityDownRequest params);

    Result<EquipProto.EquipReplaceResponse> equipReplace(EquipProto.EquipReplaceRequest params);
}
