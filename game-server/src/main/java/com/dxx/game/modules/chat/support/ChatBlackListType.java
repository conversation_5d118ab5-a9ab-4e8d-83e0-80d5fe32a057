package com.dxx.game.modules.chat.support;

import java.util.HashMap;
import java.util.Map;

public enum ChatBlackListType {
    /** 添加*/
    ADD(1),
    /** 删除*/
    REMOVE(2),
    ;

    private int type;

    private static Map<Integer, ChatBlackListType> values = new HashMap<>();

    static {
        ChatBlackListType[] values1 = values();
        for (ChatBlackListType type : values1) {
            values.put(type.getType(), type);
        }
    }


    ChatBlackListType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static ChatBlackListType getEnumByType(int type) {
        return values.get(type);
    }

}
