package com.dxx.game.modules.guild.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dto.GuildProto.*;

import java.util.List;

/**
 * @authoer: lsc
 * @createDate: 2023/5/25
 * @description:
 */
public interface GuildDonationService {

    /**
     * 请求道具
     * @param params
     * @return
     */
    Result<GuildDonationReqItemResponse> reqItemAction(GuildDonationReqItemRequest params);

    /**
     * 赠送道具
     * @param params
     * @return
     */
    Result<GuildDonationSendItemResponse> sendItemAction(GuildDonationSendItemRequest params);

    /**
     * 获取记录
     * @param params
     * @return
     */
    Result<GuildDonationGetRecordsResponse> getRecordsAction(GuildDonationGetRecordsRequest params);

    /**
     * 领取道具
     * @param params
     * @return
     */
    Result<GuildDonationReceiveResponse> receiveAction(GuildDonationReceiveRequest params);

    /**
     * 赠送受赠记录
     * @param parmas
     * @return
     */
    Result<GuildDonationGetOperationRecordsResponse> getOperationRecordsAction(GuildDonationGetOperationRecordsRequest parmas);

    GuildDonationDto buildGuildDonationDto(GuildUser guildUser);

    /**
     * 移除请求道具记录
     * @param userId
     * @param guildId
     */
    void removeDonationRequestItems(long guildId, long userId);

    /**
     * 获取可领取的道具
     * @param guildUser
     */
    List<List<Integer>> getReceiveItems(long guildId, long userId);
}
