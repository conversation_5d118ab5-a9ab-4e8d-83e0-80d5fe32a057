package com.dxx.game.modules.equip.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.EquipProto;
import com.dxx.game.dto.EquipProto.EquipComposeRequest;
import com.dxx.game.dto.EquipProto.EquipComposeResponse;
import com.dxx.game.dto.EquipProto.EquipDressRequest;
import com.dxx.game.dto.EquipProto.EquipDressResponse;
import com.dxx.game.modules.equip.service.EquipService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;

@ApiHandler
public class EquipHandler {

    @Autowired
    private EquipService equipService;

    @ApiMethod(command = MsgReqCommand.EquipComposeRequest, name = "装备-合成")
    public Result<EquipComposeResponse> equipCompose(Message msg) {
        EquipComposeRequest params = (EquipComposeRequest) msg;
        return equipService.equipCompose(params);
    }

    @ApiMethod(command = MsgReqCommand.EquipDressRequest, name = "装备-穿戴")
    public Result<EquipDressResponse> equipDress(Message msg) {
        EquipDressRequest params = (EquipDressRequest) msg;
        return equipService.equipDress(params);
    }

    @ApiMethod(command = MsgReqCommand.EquipOffRequest, name = "装备-脱下")
    public Result<EquipProto.EquipOffResponse> equipOff(Message msg) {
        EquipProto.EquipOffRequest params = (EquipProto.EquipOffRequest) msg;
        return equipService.equipOff(params);
    }

    @ApiMethod(command = MsgReqCommand.EquipQualityDownRequest, name = "装备-降品")
    public Result<EquipProto.EquipQualityDownResponse> qualityDown(Message msg) {
        EquipProto.EquipQualityDownRequest params = (EquipProto.EquipQualityDownRequest) msg;
        return equipService.qualityDown(params);
    }

    @ApiMethod(command = MsgReqCommand.EquipReplaceRequest, name = "装备-替换")
    public Result<EquipProto.EquipReplaceResponse> equipReplace(Message msg) {
        EquipProto.EquipReplaceRequest params = (EquipProto.EquipReplaceRequest) msg;
        return equipService.equipReplace(params);
    }
}