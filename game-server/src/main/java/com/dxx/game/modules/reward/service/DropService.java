package com.dxx.game.modules.reward.service;

import java.util.List;
import java.util.Random;

import com.dxx.game.modules.reward.model.Reward;

/**
 * 奖励掉落类
 * <AUTHOR>
 * @date 2019-12-17 14:08
 */
public interface DropService {


    List<Reward> dropRewards(int dropId, int count);

    /**
     * 随机道具
     * @param dropConfig
     * @return
     */
    List<Integer> randEquipId(List<List<Integer>> dropConfig);

    /**
     * 掉落物品配置
     */
    List<List<Integer>> dropRewardsConfig(int dropId);
    List<List<Integer>> dropRewardsConfig(int dropId, int count);

    List<List<Integer>> randItemByConfig(List<List<Integer>> dropConfigs, int dropCount);

    int randIndex(List<List<Integer>> config);

    int randIndexByWeight(List<List<Integer>> dropConfigs, List<Integer> exclude);

    List<Integer> randomDropWishSeed(int dropId, Random random);

    List<List<Integer>> randomDropsWithSeed(int dropId, Random random, int count);
}


























