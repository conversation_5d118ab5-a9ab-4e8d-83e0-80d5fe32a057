
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Equip_updateLevel {
    public Equip_updateLevel(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        Type = _buf.get("Type").getAsInt();
        level = _buf.get("level").getAsInt();
        nextID = _buf.get("nextID").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("levelupCost").getAsJsonArray(); levelupCost = new java.util.ArrayList<java.util.List<Integer>>(_json0_.size()); for(JsonElement _e0 : _json0_) { java.util.List<Integer> _v0;  { com.google.gson.JsonArray _json1_ = _e0.getAsJsonArray(); _v0 = new java.util.ArrayList<Integer>(_json1_.size()); for(JsonElement _e1 : _json1_) { int _v1;  _v1 = _e1.getAsInt();  _v0.add(_v1); }   }  levelupCost.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("ExAttributes").getAsJsonArray(); ExAttributes = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  ExAttributes.add(_v0); }   }
    }

    public static Equip_updateLevel deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Equip_updateLevel(_buf);
    }

    /**
     * 内容ID
     */
    public final int id;
    /**
     * 装备部位<br/>（从上往下，从左往右）<br/>获得ID时可以通过部位x*10000+等级
     */
    public final int Type;
    /**
     * 等级
     */
    public final int level;
    /**
     * 下一等级ID 满级为0
     */
    public final int nextID;
    /**
     * 升至下一级的消耗
     */
    public final java.util.List<java.util.List<Integer>> levelupCost;
    /**
     * 当前等级获得的额外属性<br/>(需要校验，只能包含base属性里有的类型)
     */
    public final java.util.List<String> ExAttributes;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "Type:" + Type + ","
        + "level:" + level + ","
        + "nextID:" + nextID + ","
        + "levelupCost:" + levelupCost + ","
        + "ExAttributes:" + ExAttributes + ","
        + "}";
    }
}

