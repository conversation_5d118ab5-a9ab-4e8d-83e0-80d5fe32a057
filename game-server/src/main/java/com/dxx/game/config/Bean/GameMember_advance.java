
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class GameMember_advance {
    public GameMember_advance(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        maxLevel = _buf.get("maxLevel").getAsInt();
        nextID = _buf.get("nextID").getAsInt();
        unlockSkill = _buf.get("unlockSkill").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("advanceUpCost").getAsJsonArray(); advanceUpCost = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  advanceUpCost.add(_v0); }   }
        AllAttack = _buf.get("AllAttack").getAsFloat();
        AllHPMax = _buf.get("AllHPMax").getAsFloat();
        AllDefence = _buf.get("AllDefence").getAsFloat();
    }

    public static GameMember_advance deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.GameMember_advance(_buf);
    }

    /**
     * 内容ID
     */
    public final int id;
    /**
     * 最大等级
     */
    public final int maxLevel;
    /**
     * 下一阶
     */
    public final int nextID;
    /**
     * 解锁技能序列
     */
    public final int unlockSkill;
    /**
     * 升至下一阶段消耗
     */
    public final java.util.List<String> advanceUpCost;
    /**
     * 当前阶段攻击累计提升百分比
     */
    public final float AllAttack;
    /**
     * 当前阶段血量累计提升百分比
     */
    public final float AllHPMax;
    /**
     * 当前阶段防御累计提升百分比
     */
    public final float AllDefence;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "maxLevel:" + maxLevel + ","
        + "nextID:" + nextID + ","
        + "unlockSkill:" + unlockSkill + ","
        + "advanceUpCost:" + advanceUpCost + ","
        + "AllAttack:" + AllAttack + ","
        + "AllHPMax:" + AllHPMax + ","
        + "AllDefence:" + AllDefence + ","
        + "}";
    }
}

