
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config;

import com.google.gson.JsonElement;

public final class Tables
{

    public  interface  IJsonLoader {
        JsonElement load(String file) throws java.io.IOException;
    }

    private final com.dxx.game.config.Model.Achievements_AchievementsModel _achievements_achievementsmodel;
    public com.dxx.game.config.Model.Achievements_AchievementsModel getAchievements_AchievementsModel() { return _achievements_achievementsmodel; }
    private final com.dxx.game.config.Model.Activity_ActivityModel _activity_activitymodel;
    public com.dxx.game.config.Model.Activity_ActivityModel getActivity_ActivityModel() { return _activity_activitymodel; }
    private final com.dxx.game.config.Model.Activity_TaskModel _activity_taskmodel;
    public com.dxx.game.config.Model.Activity_TaskModel getActivity_TaskModel() { return _activity_taskmodel; }
    private final com.dxx.game.config.Model.Activity_ShopModel _activity_shopmodel;
    public com.dxx.game.config.Model.Activity_ShopModel getActivity_ShopModel() { return _activity_shopmodel; }
    private final com.dxx.game.config.Model.Activity_RankModel _activity_rankmodel;
    public com.dxx.game.config.Model.Activity_RankModel getActivity_RankModel() { return _activity_rankmodel; }
    private final com.dxx.game.config.Model.Activity_ConsumeModel _activity_consumemodel;
    public com.dxx.game.config.Model.Activity_ConsumeModel getActivity_ConsumeModel() { return _activity_consumemodel; }
    private final com.dxx.game.config.Model.ActivityDive_DiveBaseModel _activitydive_divebasemodel;
    public com.dxx.game.config.Model.ActivityDive_DiveBaseModel getActivityDive_DiveBaseModel() { return _activitydive_divebasemodel; }
    private final com.dxx.game.config.Model.ActivityDive_DiveSmallGridModel _activitydive_divesmallgridmodel;
    public com.dxx.game.config.Model.ActivityDive_DiveSmallGridModel getActivityDive_DiveSmallGridModel() { return _activitydive_divesmallgridmodel; }
    private final com.dxx.game.config.Model.ActivityDive_DiveInsertGridModel _activitydive_diveinsertgridmodel;
    public com.dxx.game.config.Model.ActivityDive_DiveInsertGridModel getActivityDive_DiveInsertGridModel() { return _activitydive_diveinsertgridmodel; }
    private final com.dxx.game.config.Model.ActivityDive_DiveInitialGridModel _activitydive_diveinitialgridmodel;
    public com.dxx.game.config.Model.ActivityDive_DiveInitialGridModel getActivityDive_DiveInitialGridModel() { return _activitydive_diveinitialgridmodel; }
    private final com.dxx.game.config.Model.ActivityDive_DiveSpeicalModel _activitydive_divespeicalmodel;
    public com.dxx.game.config.Model.ActivityDive_DiveSpeicalModel getActivityDive_DiveSpeicalModel() { return _activitydive_divespeicalmodel; }
    private final com.dxx.game.config.Model.ActivityDive_DiveDynamicModel _activitydive_divedynamicmodel;
    public com.dxx.game.config.Model.ActivityDive_DiveDynamicModel getActivityDive_DiveDynamicModel() { return _activitydive_divedynamicmodel; }
    private final com.dxx.game.config.Model.ActivityFishing_fishingBaseModel _activityfishing_fishingbasemodel;
    public com.dxx.game.config.Model.ActivityFishing_fishingBaseModel getActivityFishing_fishingBaseModel() { return _activityfishing_fishingbasemodel; }
    private final com.dxx.game.config.Model.ActivityFishing_fishModel _activityfishing_fishmodel;
    public com.dxx.game.config.Model.ActivityFishing_fishModel getActivityFishing_fishModel() { return _activityfishing_fishmodel; }
    private final com.dxx.game.config.Model.ActivityFishing_fishRodModel _activityfishing_fishrodmodel;
    public com.dxx.game.config.Model.ActivityFishing_fishRodModel getActivityFishing_fishRodModel() { return _activityfishing_fishrodmodel; }
    private final com.dxx.game.config.Model.ActivityFishing_fishMoveModel _activityfishing_fishmovemodel;
    public com.dxx.game.config.Model.ActivityFishing_fishMoveModel getActivityFishing_fishMoveModel() { return _activityfishing_fishmovemodel; }
    private final com.dxx.game.config.Model.ActivityFlipCard_FlipBaseModel _activityflipcard_flipbasemodel;
    public com.dxx.game.config.Model.ActivityFlipCard_FlipBaseModel getActivityFlipCard_FlipBaseModel() { return _activityflipcard_flipbasemodel; }
    private final com.dxx.game.config.Model.ActivityFlipCard_FlipMapModel _activityflipcard_flipmapmodel;
    public com.dxx.game.config.Model.ActivityFlipCard_FlipMapModel getActivityFlipCard_FlipMapModel() { return _activityflipcard_flipmapmodel; }
    private final com.dxx.game.config.Model.ActivityFlipCard_FlipDynamicLayerModel _activityflipcard_flipdynamiclayermodel;
    public com.dxx.game.config.Model.ActivityFlipCard_FlipDynamicLayerModel getActivityFlipCard_FlipDynamicLayerModel() { return _activityflipcard_flipdynamiclayermodel; }
    private final com.dxx.game.config.Model.ActivityFlipCard_cardItemModel _activityflipcard_carditemmodel;
    public com.dxx.game.config.Model.ActivityFlipCard_cardItemModel getActivityFlipCard_cardItemModel() { return _activityflipcard_carditemmodel; }
    private final com.dxx.game.config.Model.ActivityPower_PowerBaseModel _activitypower_powerbasemodel;
    public com.dxx.game.config.Model.ActivityPower_PowerBaseModel getActivityPower_PowerBaseModel() { return _activitypower_powerbasemodel; }
    private final com.dxx.game.config.Model.ActivityRank_powerRewardModel _activityrank_powerrewardmodel;
    public com.dxx.game.config.Model.ActivityRank_powerRewardModel getActivityRank_powerRewardModel() { return _activityrank_powerrewardmodel; }
    private final com.dxx.game.config.Model.ActivityRank_rankRewardModel _activityrank_rankrewardmodel;
    public com.dxx.game.config.Model.ActivityRank_rankRewardModel getActivityRank_rankRewardModel() { return _activityrank_rankrewardmodel; }
    private final com.dxx.game.config.Model.Avatar_AvatarModel _avatar_avatarmodel;
    public com.dxx.game.config.Model.Avatar_AvatarModel getAvatar_AvatarModel() { return _avatar_avatarmodel; }
    private final com.dxx.game.config.Model.Book_BookModel _book_bookmodel;
    public com.dxx.game.config.Model.Book_BookModel getBook_BookModel() { return _book_bookmodel; }
    private final com.dxx.game.config.Model.Book_BookAttributeModel _book_bookattributemodel;
    public com.dxx.game.config.Model.Book_BookAttributeModel getBook_BookAttributeModel() { return _book_bookattributemodel; }
    private final com.dxx.game.config.Model.Book_bondModel _book_bondmodel;
    public com.dxx.game.config.Model.Book_bondModel getBook_bondModel() { return _book_bondmodel; }
    private final com.dxx.game.config.Model.Box_boxBaseModel _box_boxbasemodel;
    public com.dxx.game.config.Model.Box_boxBaseModel getBox_boxBaseModel() { return _box_boxbasemodel; }
    private final com.dxx.game.config.Model.Box_outputModel _box_outputmodel;
    public com.dxx.game.config.Model.Box_outputModel getBox_outputModel() { return _box_outputmodel; }
    private final com.dxx.game.config.Model.Equip_equipModel _equip_equipmodel;
    public com.dxx.game.config.Model.Equip_equipModel getEquip_equipModel() { return _equip_equipmodel; }
    private final com.dxx.game.config.Model.Equip_equipTypeModel _equip_equiptypemodel;
    public com.dxx.game.config.Model.Equip_equipTypeModel getEquip_equipTypeModel() { return _equip_equiptypemodel; }
    private final com.dxx.game.config.Model.Equip_updateLevelModel _equip_updatelevelmodel;
    public com.dxx.game.config.Model.Equip_updateLevelModel getEquip_updateLevelModel() { return _equip_updatelevelmodel; }
    private final com.dxx.game.config.Model.Equip_equipComposeModel _equip_equipcomposemodel;
    public com.dxx.game.config.Model.Equip_equipComposeModel getEquip_equipComposeModel() { return _equip_equipcomposemodel; }
    private final com.dxx.game.config.Model.Equip_skillModel _equip_skillmodel;
    public com.dxx.game.config.Model.Equip_skillModel getEquip_skillModel() { return _equip_skillmodel; }
    private final com.dxx.game.config.Model.Function_FunctionModel _function_functionmodel;
    public com.dxx.game.config.Model.Function_FunctionModel getFunction_FunctionModel() { return _function_functionmodel; }
    private final com.dxx.game.config.Model.Gacha_GachaPoolModel _gacha_gachapoolmodel;
    public com.dxx.game.config.Model.Gacha_GachaPoolModel getGacha_GachaPoolModel() { return _gacha_gachapoolmodel; }
    private final com.dxx.game.config.Model.GameConfig_AdInfoModel _gameconfig_adinfomodel;
    public com.dxx.game.config.Model.GameConfig_AdInfoModel getGameConfig_AdInfoModel() { return _gameconfig_adinfomodel; }
    private final com.dxx.game.config.Model.GameConfig_ConfigModel _gameconfig_configmodel;
    public com.dxx.game.config.Model.GameConfig_ConfigModel getGameConfig_ConfigModel() { return _gameconfig_configmodel; }
    private final com.dxx.game.config.Model.GameMember_starModel _gamemember_starmodel;
    public com.dxx.game.config.Model.GameMember_starModel getGameMember_starModel() { return _gamemember_starmodel; }
    private final com.dxx.game.config.Model.GameMember_advanceModel _gamemember_advancemodel;
    public com.dxx.game.config.Model.GameMember_advanceModel getGameMember_advanceModel() { return _gamemember_advancemodel; }
    private final com.dxx.game.config.Model.GameMember_levelupModel _gamemember_levelupmodel;
    public com.dxx.game.config.Model.GameMember_levelupModel getGameMember_levelupModel() { return _gamemember_levelupmodel; }
    private final com.dxx.game.config.Model.Guild_guildConstModel _guild_guildconstmodel;
    public com.dxx.game.config.Model.Guild_guildConstModel getGuild_guildConstModel() { return _guild_guildconstmodel; }
    private final com.dxx.game.config.Model.Guild_guildLanguageModel _guild_guildlanguagemodel;
    public com.dxx.game.config.Model.Guild_guildLanguageModel getGuild_guildLanguageModel() { return _guild_guildlanguagemodel; }
    private final com.dxx.game.config.Model.Guild_guildPowerModel _guild_guildpowermodel;
    public com.dxx.game.config.Model.Guild_guildPowerModel getGuild_guildPowerModel() { return _guild_guildpowermodel; }
    private final com.dxx.game.config.Model.Guild_guildStyleModel _guild_guildstylemodel;
    public com.dxx.game.config.Model.Guild_guildStyleModel getGuild_guildStyleModel() { return _guild_guildstylemodel; }
    private final com.dxx.game.config.Model.Guild_guildLevelModel _guild_guildlevelmodel;
    public com.dxx.game.config.Model.Guild_guildLevelModel getGuild_guildLevelModel() { return _guild_guildlevelmodel; }
    private final com.dxx.game.config.Model.Guild_guildSignInModel _guild_guildsigninmodel;
    public com.dxx.game.config.Model.Guild_guildSignInModel getGuild_guildSignInModel() { return _guild_guildsigninmodel; }
    private final com.dxx.game.config.Model.Guild_guildTaskModel _guild_guildtaskmodel;
    public com.dxx.game.config.Model.Guild_guildTaskModel getGuild_guildTaskModel() { return _guild_guildtaskmodel; }
    private final com.dxx.game.config.Model.Guild_guildShopModel _guild_guildshopmodel;
    public com.dxx.game.config.Model.Guild_guildShopModel getGuild_guildShopModel() { return _guild_guildshopmodel; }
    private final com.dxx.game.config.Model.Guild_guildEventModel _guild_guildeventmodel;
    public com.dxx.game.config.Model.Guild_guildEventModel getGuild_guildEventModel() { return _guild_guildeventmodel; }
    private final com.dxx.game.config.Model.Guild_guildDonationModel _guild_guilddonationmodel;
    public com.dxx.game.config.Model.Guild_guildDonationModel getGuild_guildDonationModel() { return _guild_guilddonationmodel; }
    private final com.dxx.game.config.Model.GuildTech_GuildTechModel _guildtech_guildtechmodel;
    public com.dxx.game.config.Model.GuildTech_GuildTechModel getGuildTech_GuildTechModel() { return _guildtech_guildtechmodel; }
    private final com.dxx.game.config.Model.HeroLevelup_HeroLevelupModel _herolevelup_herolevelupmodel;
    public com.dxx.game.config.Model.HeroLevelup_HeroLevelupModel getHeroLevelup_HeroLevelupModel() { return _herolevelup_herolevelupmodel; }
    private final com.dxx.game.config.Model.IAP_IAPModel _iap_iapmodel;
    public com.dxx.game.config.Model.IAP_IAPModel getIAP_IAPModel() { return _iap_iapmodel; }
    private final com.dxx.game.config.Model.IAP_PurchaseModel _iap_purchasemodel;
    public com.dxx.game.config.Model.IAP_PurchaseModel getIAP_PurchaseModel() { return _iap_purchasemodel; }
    private final com.dxx.game.config.Model.IAP_PushPacksModel _iap_pushpacksmodel;
    public com.dxx.game.config.Model.IAP_PushPacksModel getIAP_PushPacksModel() { return _iap_pushpacksmodel; }
    private final com.dxx.game.config.Model.IAP_DiamondsModel _iap_diamondsmodel;
    public com.dxx.game.config.Model.IAP_DiamondsModel getIAP_DiamondsModel() { return _iap_diamondsmodel; }
    private final com.dxx.game.config.Model.IAP_GiftPacksModel _iap_giftpacksmodel;
    public com.dxx.game.config.Model.IAP_GiftPacksModel getIAP_GiftPacksModel() { return _iap_giftpacksmodel; }
    private final com.dxx.game.config.Model.IAP_MonthCardModel _iap_monthcardmodel;
    public com.dxx.game.config.Model.IAP_MonthCardModel getIAP_MonthCardModel() { return _iap_monthcardmodel; }
    private final com.dxx.game.config.Model.IAP_LevelFundModel _iap_levelfundmodel;
    public com.dxx.game.config.Model.IAP_LevelFundModel getIAP_LevelFundModel() { return _iap_levelfundmodel; }
    private final com.dxx.game.config.Model.IAP_LevelFundRewardModel _iap_levelfundrewardmodel;
    public com.dxx.game.config.Model.IAP_LevelFundRewardModel getIAP_LevelFundRewardModel() { return _iap_levelfundrewardmodel; }
    private final com.dxx.game.config.Model.IAP_BattlePassModel _iap_battlepassmodel;
    public com.dxx.game.config.Model.IAP_BattlePassModel getIAP_BattlePassModel() { return _iap_battlepassmodel; }
    private final com.dxx.game.config.Model.IAP_BattlePassRewardModel _iap_battlepassrewardmodel;
    public com.dxx.game.config.Model.IAP_BattlePassRewardModel getIAP_BattlePassRewardModel() { return _iap_battlepassrewardmodel; }
    private final com.dxx.game.config.Model.IntegralShop_dataModel _integralshop_datamodel;
    public com.dxx.game.config.Model.IntegralShop_dataModel getIntegralShop_dataModel() { return _integralshop_datamodel; }
    private final com.dxx.game.config.Model.IntegralShop_goodsModel _integralshop_goodsmodel;
    public com.dxx.game.config.Model.IntegralShop_goodsModel getIntegralShop_goodsModel() { return _integralshop_goodsmodel; }
    private final com.dxx.game.config.Model.Item_ItemModel _item_itemmodel;
    public com.dxx.game.config.Model.Item_ItemModel getItem_ItemModel() { return _item_itemmodel; }
    private final com.dxx.game.config.Model.Item_dropModel _item_dropmodel;
    public com.dxx.game.config.Model.Item_dropModel getItem_dropModel() { return _item_dropmodel; }
    private final com.dxx.game.config.Model.Item_dropLvModel _item_droplvmodel;
    public com.dxx.game.config.Model.Item_dropLvModel getItem_dropLvModel() { return _item_droplvmodel; }
    private final com.dxx.game.config.Model.Item_battleModel _item_battlemodel;
    public com.dxx.game.config.Model.Item_battleModel getItem_battleModel() { return _item_battlemodel; }
    private final com.dxx.game.config.Model.ItemGift_ItemGiftModel _itemgift_itemgiftmodel;
    public com.dxx.game.config.Model.ItemGift_ItemGiftModel getItemGift_ItemGiftModel() { return _itemgift_itemgiftmodel; }
    private final com.dxx.game.config.Model.MainLevelReward_AFKrewardModel _mainlevelreward_afkrewardmodel;
    public com.dxx.game.config.Model.MainLevelReward_AFKrewardModel getMainLevelReward_AFKrewardModel() { return _mainlevelreward_afkrewardmodel; }
    private final com.dxx.game.config.Model.MainLevelReward_MainLevelChestModel _mainlevelreward_mainlevelchestmodel;
    public com.dxx.game.config.Model.MainLevelReward_MainLevelChestModel getMainLevelReward_MainLevelChestModel() { return _mainlevelreward_mainlevelchestmodel; }
    private final com.dxx.game.config.Model.PlayerAvatar_PlayerAvatarModel _playeravatar_playeravatarmodel;
    public com.dxx.game.config.Model.PlayerAvatar_PlayerAvatarModel getPlayerAvatar_PlayerAvatarModel() { return _playeravatar_playeravatarmodel; }
    private final com.dxx.game.config.Model.PlayerAvatar_PlayerNameModel _playeravatar_playernamemodel;
    public com.dxx.game.config.Model.PlayerAvatar_PlayerNameModel getPlayerAvatar_PlayerNameModel() { return _playeravatar_playernamemodel; }
    private final com.dxx.game.config.Model.Reward_rewardModel _reward_rewardmodel;
    public com.dxx.game.config.Model.Reward_rewardModel getReward_rewardModel() { return _reward_rewardmodel; }
    private final com.dxx.game.config.Model.ServerList_serverListModel _serverlist_serverlistmodel;
    public com.dxx.game.config.Model.ServerList_serverListModel getServerList_serverListModel() { return _serverlist_serverlistmodel; }
    private final com.dxx.game.config.Model.ServerList_serverGroupModel _serverlist_servergroupmodel;
    public com.dxx.game.config.Model.ServerList_serverGroupModel getServerList_serverGroupModel() { return _serverlist_servergroupmodel; }
    private final com.dxx.game.config.Model.ServerList_serverWarModel _serverlist_serverwarmodel;
    public com.dxx.game.config.Model.ServerList_serverWarModel getServerList_serverWarModel() { return _serverlist_serverwarmodel; }
    private final com.dxx.game.config.Model.SevenDay_SevenDayTaskModel _sevenday_sevendaytaskmodel;
    public com.dxx.game.config.Model.SevenDay_SevenDayTaskModel getSevenDay_SevenDayTaskModel() { return _sevenday_sevendaytaskmodel; }
    private final com.dxx.game.config.Model.SevenDay_SevenDayActiveRewardModel _sevenday_sevendayactiverewardmodel;
    public com.dxx.game.config.Model.SevenDay_SevenDayActiveRewardModel getSevenDay_SevenDayActiveRewardModel() { return _sevenday_sevendayactiverewardmodel; }
    private final com.dxx.game.config.Model.SignIn_SignInModel _signin_signinmodel;
    public com.dxx.game.config.Model.SignIn_SignInModel getSignIn_SignInModel() { return _signin_signinmodel; }
    private final com.dxx.game.config.Model.Skill_SkillModel _skill_skillmodel;
    public com.dxx.game.config.Model.Skill_SkillModel getSkill_SkillModel() { return _skill_skillmodel; }
    private final com.dxx.game.config.Model.Task_DailyTaskModel _task_dailytaskmodel;
    public com.dxx.game.config.Model.Task_DailyTaskModel getTask_DailyTaskModel() { return _task_dailytaskmodel; }
    private final com.dxx.game.config.Model.Task_DailyActiveModel _task_dailyactivemodel;
    public com.dxx.game.config.Model.Task_DailyActiveModel getTask_DailyActiveModel() { return _task_dailyactivemodel; }
    private final com.dxx.game.config.Model.Task_WeeklyActiveModel _task_weeklyactivemodel;
    public com.dxx.game.config.Model.Task_WeeklyActiveModel getTask_WeeklyActiveModel() { return _task_weeklyactivemodel; }
    private final com.dxx.game.config.Model.Vip_vipModel _vip_vipmodel;
    public com.dxx.game.config.Model.Vip_vipModel getVip_vipModel() { return _vip_vipmodel; }
    private final com.dxx.game.config.Model.Vip_dataModel _vip_datamodel;
    public com.dxx.game.config.Model.Vip_dataModel getVip_dataModel() { return _vip_datamodel; }

    public Tables(IJsonLoader loader) throws java.io.IOException {
        _achievements_achievementsmodel = new com.dxx.game.config.Model.Achievements_AchievementsModel(loader.load("model_achievements_achievementsmodel")); 
        _activity_activitymodel = new com.dxx.game.config.Model.Activity_ActivityModel(loader.load("model_activity_activitymodel")); 
        _activity_taskmodel = new com.dxx.game.config.Model.Activity_TaskModel(loader.load("model_activity_taskmodel")); 
        _activity_shopmodel = new com.dxx.game.config.Model.Activity_ShopModel(loader.load("model_activity_shopmodel")); 
        _activity_rankmodel = new com.dxx.game.config.Model.Activity_RankModel(loader.load("model_activity_rankmodel")); 
        _activity_consumemodel = new com.dxx.game.config.Model.Activity_ConsumeModel(loader.load("model_activity_consumemodel")); 
        _activitydive_divebasemodel = new com.dxx.game.config.Model.ActivityDive_DiveBaseModel(loader.load("model_activitydive_divebasemodel")); 
        _activitydive_divesmallgridmodel = new com.dxx.game.config.Model.ActivityDive_DiveSmallGridModel(loader.load("model_activitydive_divesmallgridmodel")); 
        _activitydive_diveinsertgridmodel = new com.dxx.game.config.Model.ActivityDive_DiveInsertGridModel(loader.load("model_activitydive_diveinsertgridmodel")); 
        _activitydive_diveinitialgridmodel = new com.dxx.game.config.Model.ActivityDive_DiveInitialGridModel(loader.load("model_activitydive_diveinitialgridmodel")); 
        _activitydive_divespeicalmodel = new com.dxx.game.config.Model.ActivityDive_DiveSpeicalModel(loader.load("model_activitydive_divespeicalmodel")); 
        _activitydive_divedynamicmodel = new com.dxx.game.config.Model.ActivityDive_DiveDynamicModel(loader.load("model_activitydive_divedynamicmodel")); 
        _activityfishing_fishingbasemodel = new com.dxx.game.config.Model.ActivityFishing_fishingBaseModel(loader.load("model_activityfishing_fishingbasemodel")); 
        _activityfishing_fishmodel = new com.dxx.game.config.Model.ActivityFishing_fishModel(loader.load("model_activityfishing_fishmodel")); 
        _activityfishing_fishrodmodel = new com.dxx.game.config.Model.ActivityFishing_fishRodModel(loader.load("model_activityfishing_fishrodmodel")); 
        _activityfishing_fishmovemodel = new com.dxx.game.config.Model.ActivityFishing_fishMoveModel(loader.load("model_activityfishing_fishmovemodel")); 
        _activityflipcard_flipbasemodel = new com.dxx.game.config.Model.ActivityFlipCard_FlipBaseModel(loader.load("model_activityflipcard_flipbasemodel")); 
        _activityflipcard_flipmapmodel = new com.dxx.game.config.Model.ActivityFlipCard_FlipMapModel(loader.load("model_activityflipcard_flipmapmodel")); 
        _activityflipcard_flipdynamiclayermodel = new com.dxx.game.config.Model.ActivityFlipCard_FlipDynamicLayerModel(loader.load("model_activityflipcard_flipdynamiclayermodel")); 
        _activityflipcard_carditemmodel = new com.dxx.game.config.Model.ActivityFlipCard_cardItemModel(loader.load("model_activityflipcard_carditemmodel")); 
        _activitypower_powerbasemodel = new com.dxx.game.config.Model.ActivityPower_PowerBaseModel(loader.load("model_activitypower_powerbasemodel")); 
        _activityrank_powerrewardmodel = new com.dxx.game.config.Model.ActivityRank_powerRewardModel(loader.load("model_activityrank_powerrewardmodel")); 
        _activityrank_rankrewardmodel = new com.dxx.game.config.Model.ActivityRank_rankRewardModel(loader.load("model_activityrank_rankrewardmodel")); 
        _avatar_avatarmodel = new com.dxx.game.config.Model.Avatar_AvatarModel(loader.load("model_avatar_avatarmodel")); 
        _book_bookmodel = new com.dxx.game.config.Model.Book_BookModel(loader.load("model_book_bookmodel")); 
        _book_bookattributemodel = new com.dxx.game.config.Model.Book_BookAttributeModel(loader.load("model_book_bookattributemodel")); 
        _book_bondmodel = new com.dxx.game.config.Model.Book_bondModel(loader.load("model_book_bondmodel")); 
        _box_boxbasemodel = new com.dxx.game.config.Model.Box_boxBaseModel(loader.load("model_box_boxbasemodel")); 
        _box_outputmodel = new com.dxx.game.config.Model.Box_outputModel(loader.load("model_box_outputmodel")); 
        _equip_equipmodel = new com.dxx.game.config.Model.Equip_equipModel(loader.load("model_equip_equipmodel")); 
        _equip_equiptypemodel = new com.dxx.game.config.Model.Equip_equipTypeModel(loader.load("model_equip_equiptypemodel")); 
        _equip_updatelevelmodel = new com.dxx.game.config.Model.Equip_updateLevelModel(loader.load("model_equip_updatelevelmodel")); 
        _equip_equipcomposemodel = new com.dxx.game.config.Model.Equip_equipComposeModel(loader.load("model_equip_equipcomposemodel")); 
        _equip_skillmodel = new com.dxx.game.config.Model.Equip_skillModel(loader.load("model_equip_skillmodel")); 
        _function_functionmodel = new com.dxx.game.config.Model.Function_FunctionModel(loader.load("model_function_functionmodel")); 
        _gacha_gachapoolmodel = new com.dxx.game.config.Model.Gacha_GachaPoolModel(loader.load("model_gacha_gachapoolmodel")); 
        _gameconfig_adinfomodel = new com.dxx.game.config.Model.GameConfig_AdInfoModel(loader.load("model_gameconfig_adinfomodel")); 
        _gameconfig_configmodel = new com.dxx.game.config.Model.GameConfig_ConfigModel(loader.load("model_gameconfig_configmodel")); 
        _gamemember_starmodel = new com.dxx.game.config.Model.GameMember_starModel(loader.load("model_gamemember_starmodel")); 
        _gamemember_advancemodel = new com.dxx.game.config.Model.GameMember_advanceModel(loader.load("model_gamemember_advancemodel")); 
        _gamemember_levelupmodel = new com.dxx.game.config.Model.GameMember_levelupModel(loader.load("model_gamemember_levelupmodel")); 
        _guild_guildconstmodel = new com.dxx.game.config.Model.Guild_guildConstModel(loader.load("model_guild_guildconstmodel")); 
        _guild_guildlanguagemodel = new com.dxx.game.config.Model.Guild_guildLanguageModel(loader.load("model_guild_guildlanguagemodel")); 
        _guild_guildpowermodel = new com.dxx.game.config.Model.Guild_guildPowerModel(loader.load("model_guild_guildpowermodel")); 
        _guild_guildstylemodel = new com.dxx.game.config.Model.Guild_guildStyleModel(loader.load("model_guild_guildstylemodel")); 
        _guild_guildlevelmodel = new com.dxx.game.config.Model.Guild_guildLevelModel(loader.load("model_guild_guildlevelmodel")); 
        _guild_guildsigninmodel = new com.dxx.game.config.Model.Guild_guildSignInModel(loader.load("model_guild_guildsigninmodel")); 
        _guild_guildtaskmodel = new com.dxx.game.config.Model.Guild_guildTaskModel(loader.load("model_guild_guildtaskmodel")); 
        _guild_guildshopmodel = new com.dxx.game.config.Model.Guild_guildShopModel(loader.load("model_guild_guildshopmodel")); 
        _guild_guildeventmodel = new com.dxx.game.config.Model.Guild_guildEventModel(loader.load("model_guild_guildeventmodel")); 
        _guild_guilddonationmodel = new com.dxx.game.config.Model.Guild_guildDonationModel(loader.load("model_guild_guilddonationmodel")); 
        _guildtech_guildtechmodel = new com.dxx.game.config.Model.GuildTech_GuildTechModel(loader.load("model_guildtech_guildtechmodel")); 
        _herolevelup_herolevelupmodel = new com.dxx.game.config.Model.HeroLevelup_HeroLevelupModel(loader.load("model_herolevelup_herolevelupmodel")); 
        _iap_iapmodel = new com.dxx.game.config.Model.IAP_IAPModel(loader.load("model_iap_iapmodel")); 
        _iap_purchasemodel = new com.dxx.game.config.Model.IAP_PurchaseModel(loader.load("model_iap_purchasemodel")); 
        _iap_pushpacksmodel = new com.dxx.game.config.Model.IAP_PushPacksModel(loader.load("model_iap_pushpacksmodel")); 
        _iap_diamondsmodel = new com.dxx.game.config.Model.IAP_DiamondsModel(loader.load("model_iap_diamondsmodel")); 
        _iap_giftpacksmodel = new com.dxx.game.config.Model.IAP_GiftPacksModel(loader.load("model_iap_giftpacksmodel")); 
        _iap_monthcardmodel = new com.dxx.game.config.Model.IAP_MonthCardModel(loader.load("model_iap_monthcardmodel")); 
        _iap_levelfundmodel = new com.dxx.game.config.Model.IAP_LevelFundModel(loader.load("model_iap_levelfundmodel")); 
        _iap_levelfundrewardmodel = new com.dxx.game.config.Model.IAP_LevelFundRewardModel(loader.load("model_iap_levelfundrewardmodel")); 
        _iap_battlepassmodel = new com.dxx.game.config.Model.IAP_BattlePassModel(loader.load("model_iap_battlepassmodel")); 
        _iap_battlepassrewardmodel = new com.dxx.game.config.Model.IAP_BattlePassRewardModel(loader.load("model_iap_battlepassrewardmodel")); 
        _integralshop_datamodel = new com.dxx.game.config.Model.IntegralShop_dataModel(loader.load("model_integralshop_datamodel")); 
        _integralshop_goodsmodel = new com.dxx.game.config.Model.IntegralShop_goodsModel(loader.load("model_integralshop_goodsmodel")); 
        _item_itemmodel = new com.dxx.game.config.Model.Item_ItemModel(loader.load("model_item_itemmodel")); 
        _item_dropmodel = new com.dxx.game.config.Model.Item_dropModel(loader.load("model_item_dropmodel")); 
        _item_droplvmodel = new com.dxx.game.config.Model.Item_dropLvModel(loader.load("model_item_droplvmodel")); 
        _item_battlemodel = new com.dxx.game.config.Model.Item_battleModel(loader.load("model_item_battlemodel")); 
        _itemgift_itemgiftmodel = new com.dxx.game.config.Model.ItemGift_ItemGiftModel(loader.load("model_itemgift_itemgiftmodel")); 
        _mainlevelreward_afkrewardmodel = new com.dxx.game.config.Model.MainLevelReward_AFKrewardModel(loader.load("model_mainlevelreward_afkrewardmodel")); 
        _mainlevelreward_mainlevelchestmodel = new com.dxx.game.config.Model.MainLevelReward_MainLevelChestModel(loader.load("model_mainlevelreward_mainlevelchestmodel")); 
        _playeravatar_playeravatarmodel = new com.dxx.game.config.Model.PlayerAvatar_PlayerAvatarModel(loader.load("model_playeravatar_playeravatarmodel")); 
        _playeravatar_playernamemodel = new com.dxx.game.config.Model.PlayerAvatar_PlayerNameModel(loader.load("model_playeravatar_playernamemodel")); 
        _reward_rewardmodel = new com.dxx.game.config.Model.Reward_rewardModel(loader.load("model_reward_rewardmodel")); 
        _serverlist_serverlistmodel = new com.dxx.game.config.Model.ServerList_serverListModel(loader.load("model_serverlist_serverlistmodel")); 
        _serverlist_servergroupmodel = new com.dxx.game.config.Model.ServerList_serverGroupModel(loader.load("model_serverlist_servergroupmodel")); 
        _serverlist_serverwarmodel = new com.dxx.game.config.Model.ServerList_serverWarModel(loader.load("model_serverlist_serverwarmodel")); 
        _sevenday_sevendaytaskmodel = new com.dxx.game.config.Model.SevenDay_SevenDayTaskModel(loader.load("model_sevenday_sevendaytaskmodel")); 
        _sevenday_sevendayactiverewardmodel = new com.dxx.game.config.Model.SevenDay_SevenDayActiveRewardModel(loader.load("model_sevenday_sevendayactiverewardmodel")); 
        _signin_signinmodel = new com.dxx.game.config.Model.SignIn_SignInModel(loader.load("model_signin_signinmodel")); 
        _skill_skillmodel = new com.dxx.game.config.Model.Skill_SkillModel(loader.load("model_skill_skillmodel")); 
        _task_dailytaskmodel = new com.dxx.game.config.Model.Task_DailyTaskModel(loader.load("model_task_dailytaskmodel")); 
        _task_dailyactivemodel = new com.dxx.game.config.Model.Task_DailyActiveModel(loader.load("model_task_dailyactivemodel")); 
        _task_weeklyactivemodel = new com.dxx.game.config.Model.Task_WeeklyActiveModel(loader.load("model_task_weeklyactivemodel")); 
        _vip_vipmodel = new com.dxx.game.config.Model.Vip_vipModel(loader.load("model_vip_vipmodel")); 
        _vip_datamodel = new com.dxx.game.config.Model.Vip_dataModel(loader.load("model_vip_datamodel")); 
    }
}

