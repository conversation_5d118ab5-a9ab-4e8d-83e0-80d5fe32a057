
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class GameMember_levelup {
    public GameMember_levelup(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("levelUpCost").getAsJsonArray(); levelUpCost = new java.util.ArrayList<java.util.List<Integer>>(_json0_.size()); for(JsonElement _e0 : _json0_) { java.util.List<Integer> _v0;  { com.google.gson.JsonArray _json1_ = _e0.getAsJsonArray(); _v0 = new java.util.ArrayList<Integer>(_json1_.size()); for(JsonElement _e1 : _json1_) { int _v1;  _v1 = _e1.getAsInt();  _v0.add(_v1); }   }  levelUpCost.add(_v0); }   }
        nextID = _buf.get("nextID").getAsInt();
    }

    public static GameMember_levelup deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.GameMember_levelup(_buf);
    }

    /**
     * 英雄等级
     */
    public final int ID;
    /**
     * 升至下一级时的消耗
     */
    public final java.util.List<java.util.List<Integer>> levelUpCost;
    /**
     * 下一等级
     */
    public final int nextID;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "levelUpCost:" + levelUpCost + ","
        + "nextID:" + nextID + ","
        + "}";
    }
}

