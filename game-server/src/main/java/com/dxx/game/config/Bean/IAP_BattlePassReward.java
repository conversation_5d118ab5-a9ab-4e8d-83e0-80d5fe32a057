
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class IAP_BattlePassReward {
    public IAP_BattlePassReward(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        level = _buf.get("level").getAsInt();
        groupId = _buf.get("groupId").getAsInt();
        type = _buf.get("type").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("freeReward").getAsJsonArray(); freeReward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  freeReward.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("battlePassReward").getAsJsonArray(); battlePassReward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  battlePassReward.add(_v0); }   }
        score = _buf.get("score").getAsInt();
    }

    public static IAP_BattlePassReward deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.IAP_BattlePassReward(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 等级
     */
    public final int level;
    /**
     * 通行证组id
     */
    public final int groupId;
    /**
     * 奖励类型：<br/>1普通奖励<br/>2大奖<br/>3最终循环奖励
     */
    public final int type;
    /**
     * 免费奖励
     */
    public final java.util.List<String> freeReward;
    /**
     * 通行证奖励
     */
    public final java.util.List<String> battlePassReward;
    /**
     * 解锁点数：<br/>奖励类型为3时，该点数为循环点数
     */
    public final int score;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "level:" + level + ","
        + "groupId:" + groupId + ","
        + "type:" + type + ","
        + "freeReward:" + freeReward + ","
        + "battlePassReward:" + battlePassReward + ","
        + "score:" + score + ","
        + "}";
    }
}

