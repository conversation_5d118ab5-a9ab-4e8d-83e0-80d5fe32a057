package com.dxx.game.common.config.game.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 *
 */
@Component
public class MatrixLongConverter implements FieldConverter {
	
	@Override
	public List<List<Long>> convert (Field field, Object val) throws Exception {
		List<List<String>> list = JSON.parseObject(val.toString(), new TypeReference<List<List<String>>>(){});
		List<List<Long>> result = new ArrayList<>();
		for (int i = 0, length = list.size(); i < length; i ++) {
			List<String> value = list.get(i);
			List<Long> newValue = new ArrayList<>();
			for (int j = 0; j < value.size(); j ++) {
				newValue.add(Long.parseLong(value.get(j)));
			}
			result.add(newValue);
		}
		return Collections.unmodifiableList(result);
	}
}
