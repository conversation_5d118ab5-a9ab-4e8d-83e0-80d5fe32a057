package com.dxx.game.common.aws.translate;

import com.dxx.game.common.aws.config.AWSConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.translate.TranslateClient;
import software.amazon.awssdk.services.translate.TranslateClientBuilder;

/**
 * @authoer: lsc
 * @createDate: 2023/4/17
 * @description:
 */
@Configuration
public class TranslateConfig extends AWSConfig {

    @Bean
    public TranslateClient translateClient() {
        TranslateClientBuilder translateClientBuilder = TranslateClient.builder()
                .region(Region.of(REGION));
        if (profileCredentialsProvider != null) {
            translateClientBuilder.credentialsProvider(profileCredentialsProvider);
        } else if (awsCredentialsProvider != null) {
            translateClientBuilder.credentialsProvider(awsCredentialsProvider);
        }

        ApacheHttpClient.Builder apacheHttpClient = ApacheHttpClient.builder()
                .maxConnections(256)
                .tcpKeepAlive(true);
        translateClientBuilder.httpClientBuilder(apacheHttpClient);
        return translateClientBuilder.build();
    }
}
