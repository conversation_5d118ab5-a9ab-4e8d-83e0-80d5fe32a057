package com.dxx.game.common.config.game;

import com.dxx.game.common.config.game.converter.FieldConverter;

import java.lang.reflect.Field;

public class FieldDescriptor {
	
	private final FieldConverter converter;
	private final Field field;
	private final int index;
	private final boolean required;

	public FieldDescriptor(int index, Field field, FieldConverter converter, boolean required) {
		this.index = index;
		this.field = field;
		this.converter = converter;
		this.required = required;
	}
	
	public FieldConverter getConverter() {
		return converter;
	}

	public Field getField() {
		return field;
	}

	public int getIndex() {
		return index;
	}

	public boolean isRequired() {
		return required;
	}
}
