package com.dxx.game.common.aws.dynamodb.transaction;

import lombok.Getter;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.mapper.BeanTableSchema;

/**
 * <AUTHOR>
 * @date 2022/5/13 7:20 下午
 */
@Getter
public class DynamoDBTableInfo<T> {

    Class<T> clazz;
    DynamoDbTable<T> mappedTable;
    BeanTableSchema<T> tableSchema;

    public DynamoDBTableInfo(Class<T> clazz, DynamoDbTable<T> mappedTable, BeanTableSchema<T> tableSchema) {
        this.clazz = clazz;
        this.mappedTable = mappedTable;
        this.tableSchema = tableSchema;
    }
}
