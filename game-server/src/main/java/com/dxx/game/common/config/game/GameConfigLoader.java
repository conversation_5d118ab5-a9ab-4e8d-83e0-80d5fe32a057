package com.dxx.game.common.config.game;

import com.dxx.game.common.aws.s3.S3Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/15 17:11
 */
@Slf4j
@Component
public class GameConfigLoader {

    @Autowired
    private S3Service s3Service;
    @Value("${game.config.path}")
    private String gameConfigPath;
    @Value("${aws.s3.game.config.bucket.name}")
    private String s3GameConfigBucketName;

    /** 所有配置object **/
    private Map<String, Class<?>> gameConfigObjClass = new HashMap<>();

    /**
     * 加载配置文件内容
     * @param configName
     * @return
     * @throws IOException
     */
    public String loadConfig(String configName) throws IOException {
        String fileContent = "";
        if (!StringUtils.isEmpty(s3GameConfigBucketName)) {
            // 从s3读取配置文件
            fileContent = this.loadConfigFromS3(configName);
        } else {
            // 本地配置
            fileContent = this.loadConfigFromLocal(configName);
        }
        return fileContent;
    }

    /**
     * 从s3读取配置文件
     * @param configName
     * @return
     */
    public String loadConfigFromS3(String configName) {
        log.info("Load s3 game config : {}", configName);
        return s3Service.loadContent(s3GameConfigBucketName, configName);
    }

    /**
     * 加载本地配置
     * @param configName
     * @return
     */
    public String loadConfigFromLocal(String configName) throws IOException {
        String filePath = gameConfigPath + configName;
        File file = new File(filePath);
        log.info("Load local game config : {}", filePath);
        return FileUtils.readFileToString(file, "UTF-8");
    }

    public void addClass(String configName, Class<?> clazz) {
        if (!this.gameConfigObjClass.containsKey(configName)) {
            this.gameConfigObjClass.put(configName, clazz);
        }
    }

    public Map<String, Class<?>> getGameConfigObjClass() {
        return gameConfigObjClass;
    }
}
