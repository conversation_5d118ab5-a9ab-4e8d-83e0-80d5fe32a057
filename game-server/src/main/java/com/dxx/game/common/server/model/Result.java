package com.dxx.game.common.server.model;

import java.util.HashMap;

import com.dxx.game.consts.ErrorCode;
import lombok.Getter;


/**
 * 返回结果对象
 * <AUTHOR>
 * @since 2019年12月5日
 */
public class Result<T> extends HashMap<String, Object> {
	
	private static final long serialVersionUID = -6033976911427250315L;
	
	/**
	 * 错误码
	 */
	public static final String CODE = "code";
	
	/**
	 * 返回的内容对象
	 */
	public static final String CONTENT = "content";

	public static final String CMD = "cmd";

	@Getter
	private boolean idempotent;
	
	/**
	 * 返回成功结果
	 * @param content
	 * @return
	 */
	public static <T> Result<T> Success(T content) {
		Result<T> result = new Result<T>();
		result.setCode(ErrorCode.SUCCESS);
		result.setContent(content);
		return result;
	}

	/**
	 * 返回成功结果
	 *
	 * @param content
	 * @return
	 */
	public static <T> Result<T> Idempotence(T content) {
		Result<T> result = new Result<T>();
		result.setCode(ErrorCode.SUCCESS);
		result.setContent(content);
		result.idempotent = true;
		return result;
	}
	
	/**
	 * 返回失败结果
	 * @param errorCode
	 * @return
	 */
	public static <T> Result<T> Error(int errorCode) {
		Result<T> result = new Result<T>();
		result.setCode(errorCode);
		return result;
	}
	
	/**
	 * 设置内容对象
	 * @param content
	 */
	public void setContent(Object content) {
		this.put(CONTENT, content);
	}
	/**
	 * 返回内容对象
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public T getContent() {
		return (T) this.get(CONTENT);
	}
	
	/**
	 * 设置错误码
	 * @param code
	 */
	public void setCode(int code) {
		this.put(CODE, code);
	}
	
	/**
	 * 获取 错误码
	 */
	public int getCode() {
		return (int) this.get(CODE);
	}

	public void setCmd(short cmd) {
		this.put(CMD, cmd);
	}

	public short getCmd() {
		return (short) this.getOrDefault(CMD, (short) 0);
	}
}










