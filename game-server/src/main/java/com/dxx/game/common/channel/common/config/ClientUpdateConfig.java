package com.dxx.game.common.channel.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.config.game.GameConfigLoader;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @authoer: lsc
 * @createDate: 2022/6/11
 * @description:
 */
@Slf4j
@Getter
@Component
public class ClientUpdateConfig {

    @Autowired
    private GameConfigLoader gameConfigLoader;


    private ConcurrentHashMap<String, ClientUpdateEntity> clientUpdateAddress = new ConcurrentHashMap<>();

    @PostConstruct
    public void loadConfig() {
//        try {
//            String content = gameConfigLoader.loadConfig("ClientUpdateAddress.json");
//            this.setUp(content);
//        } catch (Exception e) {
//            log.error("load ClientUpdateAddress Error,", e);
//        }
    }

    private void setUp(String content) {
        JSONObject jsonObject = JSON.parseObject(content);
        JSONObject data = jsonObject.getJSONObject("ClientUpdateAddress");

        for (Map.Entry<String, Object> entry : data.entrySet()) {
            JSONObject value = (JSONObject) entry.getValue();

            String packageName = value.getString("packageName");
            String packageId = value.getString("packageId");
            String url = value.getString("url");
            int netVersion = value.getIntValue("netVersion");

            ClientUpdateEntity entity = new ClientUpdateEntity();
            entity.setPackageName(packageName);
            entity.setPackageId(packageId);
            entity.setUrl(url);
            entity.setNetVersion(netVersion);
            String key = packageName + "-" + packageId;
            if (!clientUpdateAddress.containsKey(key)) {
                clientUpdateAddress.put(key, entity);
            }
        }
    }

    public ClientUpdateEntity getConfig(String packageName, String packageId) {
        String key = packageName + "-" + packageId;
        return clientUpdateAddress.get(key);
    }

    @Data
    public static class ClientUpdateEntity {
        private String packageName;
        private String packageId;
        private String url;
        private int netVersion;
    }
}
