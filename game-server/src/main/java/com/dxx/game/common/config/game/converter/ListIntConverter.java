package com.dxx.game.common.config.game.converter;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;


@Component
public class ListIntConverter implements FieldConverter {

	@Override
	public List<Integer> convert (Field field, Object val) throws Exception {
		List<String> list = JSON.parseObject(val.toString(), new TypeReference<List<String>>(){});
		List<Integer> ret = new ArrayList<>();
		for(int i = 0; i < list.size(); i ++) {
			ret.add(Integer.parseInt(list.get(i).toString()));
		}
		return Collections.unmodifiableList(ret);
	}
	
}
