package com.dxx.game.common.aws.dynamodb.annotation;

import com.dxx.game.common.server.model.Result;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 扫描指定包(basePackages)的方法， 返回值是 (methodReturnTypeClasses)
 * 并且未使用 @DynamoDBTransactional 注解
 * <AUTHOR>
 * @date 2020/10/27 10:26
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@Documented
@Import({DynamoDBTransactionScannerRegistrar.class})
public @interface DynamoDBTransactionScan {

    String[] basePackages() default {};

    Class<?>[] methodReturnTypeClasses() default {Result.class};
}
