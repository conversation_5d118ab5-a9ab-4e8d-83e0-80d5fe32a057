package com.dxx.game.common.utils;

import java.math.BigInteger;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.NetworkInterface;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 简单的IP相关工具类
 * <AUTHOR>
 * @date 2019-12-10 10:16
 */
@Slf4j
public class IpUtils {

    /**
     * ipToLong
     * @param strIp
     * @return
     */
    public static long ipToLong(String strIp) {
        String[] s = strIp.split("\\.");
        long ip = (Long.parseLong(s[0]) << 24)
                + (Long.parseLong(s[1]) << 16) +
                (Long.parseLong(s[2]) << 8)
                + (Long.parseLong(s[3]));
        return ip;
    }

    /**
     * LongToIp
     * @param longIp
     * @return
     */
    public static String longToIP(long longIp) {
        //采用SB方便追加分隔符 "."
        StringBuffer sb = new StringBuffer("");
        sb.append(String.valueOf(longIp>>24)).append(".").
                append(String.valueOf((longIp&0x00ffffff)>>16)).append(".").
                append(String.valueOf((longIp&0x0000ffff)>>8)).append(".").
                append(String.valueOf(longIp&0x000000ff));
        return sb.toString();
    }

    /**
     * ip转 BigInt
     * @param ip
     * @return
     */
    public static BigInteger ipToBigInt(String ip) {
        ip = ip.replace(" ", "");
        byte[] bytes;
        if (ip.contains(":"))
            bytes = ipv6ToBytes(ip);
        else
            bytes = ipv4ToBytes(ip);
        return new BigInteger(bytes);
    }

    /**
     * 释放是IPV6
     * @param ip
     * @return
     */
    public static boolean isIPV6(String ip) {
        return ip.contains(":");
    }
    /**
     * BigInt 转IP
     * @param ipInBigInt
     * @return
     */
    public static String bigIntToIp(BigInteger ipInBigInt) {
        byte[] bytes = ipInBigInt.toByteArray();
        byte[] unsignedBytes = Arrays.copyOfRange(bytes, 1, bytes.length);
        // 去除符号位
        try {
            String ip = InetAddress.getByAddress(unsignedBytes).toString();
            return ip.substring(ip.indexOf('/') + 1).trim();
        } catch (Exception e) {
            log.error("bigIntToIp e:", e);
            return null;
        }
    }

    /**
     * ipv6 转Bytes
     * @param ipv6
     * @return
     */
    private static byte[] ipv6ToBytes(String ipv6) {
        byte[] ret = new byte[17];
        ret[0] = 0;
        int ib = 16;
        boolean comFlag = false;// ipv4混合模式标记
        if (ipv6.startsWith(":"))// 去掉开头的冒号
            ipv6 = ipv6.substring(1);
        String groups[] = ipv6.split(":");
        for (int ig = groups.length - 1; ig > -1; ig--) {// 反向扫描
            if (groups[ig].contains(".")) {
                // 出现ipv4混合模式
                byte[] temp = ipv4ToBytes(groups[ig]);
                ret[ib--] = temp[4];
                ret[ib--] = temp[3];
                ret[ib--] = temp[2];
                ret[ib--] = temp[1];
                comFlag = true;
            } else if ("".equals(groups[ig])) {
                // 出现零长度压缩,计算缺少的组数
                int zlg = 9 - (groups.length + (comFlag ? 1 : 0));
                while (zlg-- > 0) {// 将这些组置0
                    ret[ib--] = 0;
                    ret[ib--] = 0;
                }
            } else {
                int temp = Integer.parseInt(groups[ig], 16);
                ret[ib--] = (byte) temp;
                ret[ib--] = (byte) (temp >> 8);
            }
        }
        return ret;
    }

    /**
     * ipv4 转bytes
     * @param ipv4
     * @return
     */
    private static byte[] ipv4ToBytes(String ipv4) {
        byte[] ret = new byte[5];
        ret[0] = 0;
        // 先找到IP地址字符串中.的位置
        int position1 = ipv4.indexOf(".");
        int position2 = ipv4.indexOf(".", position1 + 1);
        int position3 = ipv4.indexOf(".", position2 + 1);
        // 将每个.之间的字符串转换成整型
        ret[1] = (byte) Integer.parseInt(ipv4.substring(0, position1));
        ret[2] = (byte) Integer.parseInt(ipv4.substring(position1 + 1,
                position2));
        ret[3] = (byte) Integer.parseInt(ipv4.substring(position2 + 1,
                position3));
        ret[4] = (byte) Integer.parseInt(ipv4.substring(position3 + 1));
        return ret;
    }

    /**
     * 获取访问IP
     * @param ctx
     * @param fullRequest
     * @return
     */
    public static String getRemoteIp(ChannelHandlerContext ctx, FullHttpRequest fullRequest) {
        String clientIP = fullRequest.headers().get("X-Forwarded-For");
        if (clientIP == null) {
            InetSocketAddress insocket = (InetSocketAddress) ctx.channel()
                    .remoteAddress();
            clientIP = insocket.getAddress().getHostAddress();
        }

        return clientIP.split(",")[0];
    }

    /**
     * 获取本机服务器内网IP
     * @return
     */
    public static String getLocalIp() {
        boolean isLinux = System.getProperty("os.name").toLowerCase().contains("linux");
        String ip = "";
        try {
            if (isLinux) {
                Map<String, String> netWorkName = new HashMap<>();
                Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
                if (interfaces != null) {
                    while (interfaces.hasMoreElements()) {
                        NetworkInterface network = interfaces.nextElement();
                        if ("eth0".equals(network.getName()) || "eth1".equals(network.getName()) || "ens5".equals(network.getName())) {
                            Enumeration<InetAddress> addresses = network.getInetAddresses();
                            while (addresses.hasMoreElements()) {
                                InetAddress ia = (InetAddress) addresses.nextElement();
                                if (ia instanceof Inet6Address)
                                    continue;
                                netWorkName.put(network.getName(), ia.getHostAddress());
                            }
                        }
                    }

                    ip = netWorkName.getOrDefault("eth1", "");
                    if (ip.isEmpty()) {
                        ip = netWorkName.getOrDefault("eth0", "");
                    }
                    if (ip.isEmpty()) {
                        ip = netWorkName.getOrDefault("ens5", "");
                    }
                }
            } else {
                InetAddress addr = InetAddress.getLocalHost();
                ip = addr.getHostAddress();
            }
            return ip;
        } catch (Exception e) {
            log.error("getLocalIp e:", e);
            return "";
        }
    }

    public static void main(String[] args) {

        System.out.println(ipToBigInt("2400:da00::dbf:0:100"));
        System.out.println(ipToBigInt("**************"));

    }
}



















