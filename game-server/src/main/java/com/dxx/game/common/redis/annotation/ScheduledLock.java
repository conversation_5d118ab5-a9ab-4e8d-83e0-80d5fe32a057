package com.dxx.game.common.redis.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/11/3 10:08
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
@Documented
public @interface ScheduledLock {
    String lockKey() default "";
    String lockValue() default "1";
    long expireSeconds() default 60;
    TimeUnit timeUnit() default TimeUnit.SECONDS;
}
