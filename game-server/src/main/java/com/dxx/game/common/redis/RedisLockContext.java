package com.dxx.game.common.redis;

import io.netty.util.concurrent.FastThreadLocal;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/31 10:30
 */
public class RedisLockContext {

    private static FastThreadLocal<String> localClientId = new FastThreadLocal<String>();
    private static FastThreadLocal<String> localLockKey = new FastThreadLocal<String>();

    private static FastThreadLocal<Map<String, String>> localLockData = new FastThreadLocal<>();

    public static String getLockKey() {
        return localLockKey.get();
    }

    public static void setLockKey(String lockKey) {
        localLockKey.set(lockKey);
    }

    public static void setClientId(String clientId) {localClientId.set(clientId);}

    public static String getClientId() {return localClientId.get();}

    public static void putLockData(String lockKey, String lockValue) {
        if (localLockKey.get() != null && localLockKey.get().equals(lockKey)) {
            return;
        }
        if (localLockData.get() == null) {
            localLockData.set(new HashMap<>());
        }
        localLockData.get().put(lockKey, lockValue);
    }

    public static Map<String, String> getLockData() {
        return localLockData.get();
    }

    public static void clear() {
        localClientId.remove();
        localLockKey.remove();
        localLockData.remove();
    }
}
