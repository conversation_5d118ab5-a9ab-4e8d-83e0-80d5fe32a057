package com.dxx.game.common.aws.kinesis;

import io.netty.util.concurrent.FastThreadLocal;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/16 16:42
 */
public class KinesiseCacheManager {

    private static final FastThreadLocal<List<List<Object>>> resourceLogChache = new FastThreadLocal<>();

    public static List<List<Object>> getResourceLogChache() {
        return resourceLogChache.get();
    }

    public static void putResourceLogChache(Object... data) {
        if (resourceLogChache.get() == null) {
            resourceLogChache.set(new ArrayList<>());
        }
        resourceLogChache.get().add(Arrays.asList(data));
    }

    public static void clear() {
        resourceLogChache.remove();
    }
}
