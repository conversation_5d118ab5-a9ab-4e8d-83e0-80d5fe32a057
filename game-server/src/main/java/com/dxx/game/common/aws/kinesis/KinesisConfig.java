package com.dxx.game.common.aws.kinesis;

import com.dxx.game.common.aws.config.AWSConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.firehose.FirehoseAsyncClient;
import software.amazon.awssdk.services.firehose.FirehoseAsyncClientBuilder;

/**
 * <AUTHOR>
 * @date 2021/4/16 16:09
 */
@Configuration
public class KinesisConfig extends AWSConfig {

    @Bean
    public FirehoseAsyncClient test() {
        FirehoseAsyncClientBuilder firehoseAsyncClientBuilder
                = FirehoseAsyncClient.builder().region(Region.of(REGION));

        if (profileCredentialsProvider != null) {
            firehoseAsyncClientBuilder.credentialsProvider(profileCredentialsProvider);
        } else if (awsCredentialsProvider != null) {
            firehoseAsyncClientBuilder.credentialsProvider(awsCredentialsProvider);
        }

        NettyNioAsyncHttpClient.Builder nettyNioAsyncHttpClient =
                NettyNioAsyncHttpClient.builder().maxConcurrency(200).tcpKeepAlive(true).maxPendingConnectionAcquires(100);

        return firehoseAsyncClientBuilder.httpClientBuilder(nettyNioAsyncHttpClient).build();
    }
}
