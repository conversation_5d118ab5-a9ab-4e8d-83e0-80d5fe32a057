package com.dxx.game.common.config.game.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class ListMapConverter implements FieldConverter{

    @Override
    public List<Map<String, Object>> convert (Field field, Object val) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();
        List<Map<String, Object>> maps = JSON.parseObject(val.toString(), new TypeReference<List<Map<String, Object>>>() {});
        for (Map<String, Object> map : maps) {
            Map<String, Object> newValue = Collections.unmodifiableMap(map);
            result.add(newValue);
        }
        return Collections.unmodifiableList(result);
    }
}
