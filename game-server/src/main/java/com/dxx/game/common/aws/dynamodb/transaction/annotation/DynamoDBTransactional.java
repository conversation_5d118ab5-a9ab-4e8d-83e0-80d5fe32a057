package com.dxx.game.common.aws.dynamodb.transaction.annotation;

import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBWriteType;

import java.lang.annotation.*;

@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DynamoDBTransactional {

    /**
     * 写入方式
     * @return
     */
    DynamoDBWriteType value() default  DynamoDBWriteType.TRANSACTION_ENHANCED;

//    /**
//     * 是否使用事物写入数据
//     * @return
//     */
//    boolean open() default false;
}
