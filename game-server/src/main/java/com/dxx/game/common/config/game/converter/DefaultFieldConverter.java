package com.dxx.game.common.config.game.converter;

import java.lang.reflect.Field;

import org.springframework.stereotype.Component;


@Component
public class DefaultFieldConverter implements FieldConverter {
	
	@Override
	public Object convert(Field field, Object value) throws Exception {
		Class<?> typeClass = field.getType();
		String val = value.toString();
		if (val.isEmpty() && typeClass != String.class) {
			val = "0";
		}
		if (typeClass == Integer.class || typeClass == int.class) {
			return Integer.parseInt(val);
		} else if (typeClass == Long.class || typeClass == long.class) {
			return Long.parseLong(val);
		} else if (typeClass == Byte.class || typeClass == byte.class) {
			return Byte.parseByte(val);
		} else if (typeClass == Short.class || typeClass == short.class) {
			return Short.parseShort(val);
		} else if (typeClass == Boolean.class || typeClass == boolean.class) {
			return Boolean.parseBoolean(val);
		} else if (typeClass == Float.class || typeClass == float.class) {
			return Float.parseFloat(val);
		} else if (typeClass == Double.class || typeClass == double.class) {
			return Double.parseDouble(val);
		} else if (typeClass == String.class) {
			return val;
		}
		throw new ConfigException("Cannot parse field value.Class:" + typeClass.getName() + ";Value:" + val);
	}
}
