package com.dxx.game.common.config.game.converter;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;


@Component
public class ListStringConverter implements FieldConverter {
	
	@Override
	public List<String> convert(Field field, Object val) throws Exception {
		if (val.getClass() == String.class) {
			return new ArrayList<>();
		}
		List<String> strings = JSON.parseObject(val.toString(), new TypeReference<List<String>>() {});
		return Collections.unmodifiableList(strings);
	}
}
