package com.dxx.game.common.aws.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBTableInfo;
import lombok.Getter;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.model.WriteBatch;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/14 6:07 下午
 */
@Getter
@Setter
public class DynamoDBBatchWriteData {

    WriteBatch.Builder<DynamoDBBaseModel> writeBatchBuilder;

    DynamoDbTable<DynamoDBBaseModel> mappedTable;

    Class<DynamoDBBaseModel> clazz;

    public DynamoDBBatchWriteData(Class<DynamoDBBaseModel> clazz, DynamoDbTable<DynamoDBBaseModel> mappedTable) {
        writeBatchBuilder = WriteBatch.builder(clazz).mappedTableResource(mappedTable);
        this.mappedTable = mappedTable;
        this.clazz = clazz;
    }
}
