package com.dxx.game.dao.dynamodb.model.activity;

import lombok.Data;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.util.List;

@Data
@DynamoDbBean
public class Power extends ActivityBase {

    private PowerModel power;

    @Data
    @DynamoDbBean
    public static class PowerModel {
        /** 已领奖的id */
        private List<Integer> rewardIds;
    }
}
