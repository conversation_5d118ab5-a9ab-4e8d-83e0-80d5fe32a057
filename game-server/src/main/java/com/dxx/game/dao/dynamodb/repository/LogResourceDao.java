package com.dxx.game.dao.dynamodb.repository;

import com.alibaba.fastjson.JSONArray;
import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.common.aws.dynamodb.utils.DynamoDBConvertUtil;
import com.dxx.game.dao.dynamodb.model.LogResource;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.AttributeValueUpdate;
import software.amazon.awssdk.services.dynamodb.model.QueryRequest;
import software.amazon.awssdk.services.dynamodb.model.QueryResponse;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/9 17:02
 */
@Repository
public class LogResourceDao extends BaseDynamoDBDao<LogResource> {

    public List<LogResource> queryModify(long userId) {
        Key key = this.buildKey(userId);
        QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(key))
                .consistentRead(true)
                .scanIndexForward(false)
                .limit(100)
                .build();
        return mappedTable.query(queryEnhancedRequest).stream().findFirst().get().items();
    }

    public LogResource getByTransId(long userId, long transId) {
        return super.getItem(userId, transId);
    }

    public QueryResponse queryLog(long userId, int pageSize, int command,
                                  JSONArray logTime, Map<String, AttributeValue> exclusiveStartKey) {

        Map<String, String> expressionAttributeNames = new HashMap<>();
        expressionAttributeNames.put("#userId", "userId");
        expressionAttributeNames.put("#time", "logTime");

        long start = logTime.getLongValue(0);
        long end = logTime.getLongValue(1);

        Map<String, AttributeValue> expressionAttributeValues = new HashMap<>();
        expressionAttributeValues.put(":userId", DynamoDBConvertUtil.buildAttributeValue(userId));
        expressionAttributeValues.put(":start", DynamoDBConvertUtil.buildAttributeValue(start));
        expressionAttributeValues.put(":end", DynamoDBConvertUtil.buildAttributeValue(end));
        if (command > 0) {
            expressionAttributeNames.put("#command", "command");
            expressionAttributeValues.put(":command", DynamoDBConvertUtil.buildAttributeValue(command));
        }
        QueryRequest.Builder queryRequestBuilder = QueryRequest.builder()
                .tableName(this.tableName)
                .indexName("userId-logTime-index")
                .keyConditionExpression("#userId = :userId and #time BETWEEN :start AND :end")
                .expressionAttributeNames(expressionAttributeNames)
                .expressionAttributeValues(expressionAttributeValues)
                .scanIndexForward(false)
                .exclusiveStartKey(exclusiveStartKey.isEmpty() ? null : exclusiveStartKey)
                .limit(pageSize);

        if (command > 0) {
            queryRequestBuilder.filterExpression("#command = :command");
        }

        QueryRequest queryRequest = queryRequestBuilder.build();
        return super.dynamoDbClient.query(queryRequest);
    }

    public List<LogResource> getByTransIds(long userId, List<Long> transIds) {
        return super.batchGetItem(userId, transIds);
    }
}
