package com.dxx.game.dao.dynamodb.model.guild;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.*;

/**
 * @authoer: lsc
 * @createDate: 2023/3/24
 * @description:
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("guild")
public class GuildApply extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private String PK;               // 主键
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private String SK;                 // 排序键

    @Getter(onMethod_ = {@DynamoDbSecondaryPartitionKey(indexNames = "guildId-index")})
    private Long guildId;
    @Getter(onMethod_ = {@DynamoDbSecondarySortKey(indexNames = "guildId-index")})
    private String SK2;

    private Long userId;
    private Long applyTime;             // 申请时间

    @DynamoDbIgnore
    @Override
    public Object getUniqueKey() {
        return this.PK + "_" + this.SK;
    }
}
