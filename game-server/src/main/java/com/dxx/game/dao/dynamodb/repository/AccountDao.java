package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.dao.dynamodb.model.Account;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;

import jakarta.annotation.PostConstruct;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/19 10:44
 */
@Repository
public class AccountDao extends BaseDynamoDBDao<Account> {

    DynamoDbIndex<Account> accountIdIndex;
    DynamoDbIndex<Account> deviceIdIndex;

    @PostConstruct
    private void init() {
        accountIdIndex = this.mappedTable.index("accountId-Index");
        deviceIdIndex = this.mappedTable.index("deviceId-Index");
    }


    /**
     * 根据账号ID获取用户
     *
     * @param accountId
     * @return
     */
    public Account getByAccountId(String accountId) {
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(accountId).build());
        List<Page<Account>> collect = accountIdIndex.query(queryConditional).stream().collect(Collectors.toList());
        List<Account> items = collect.get(0).items();
        if (items.size() == 0) {
            return null;
        }
        String key = items.get(0).getPK();
        return this.getItem(key);

    }

    /**
     * 根据设备ID获取用户
     *
     * @param deviceId
     * @return
     */
    public Account getByDeviceId(String deviceId) {
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(deviceId).build());
        List<Page<Account>> collect = deviceIdIndex.query(queryConditional).stream().collect(Collectors.toList());
        List<Account> items = collect.get(0).items();
        if (items.size() == 0) {
            return null;
        }
        items.stream().sorted(getAccountComparator());
        String key = items.get(0).getPK();
        return this.getItem(key);
    }
    private static Comparator<Account> getAccountComparator() {
        return (c1, c2) -> (int) (c2.getLastLoginTime() - c1.getLastLoginTime());
    }



    public void updateChannelId(Account account) {
        super.updateIgnoreNulls(account, update -> update.setChannelId(account.getChannelId()));
    }

    public Account getByAccountKey(String accountKey) {
        return getItem(accountKey);
    }

    public void updateAccountId(Account account) {
        if (StringUtils.isEmpty(account.getAccountId())) {
            return;
        }
        super.updateIgnoreNulls(account, update -> update.setAccountId(account.getAccountId()));
    }

    public void updateAccountId2(Account account) {
        if (StringUtils.isEmpty(account.getAccountId2())) {
            return;
        }
        super.updateIgnoreNulls(account, update -> update.setAccountId2(account.getAccountId2()));
    }

    public List<Account> getAllAccountByDeviceId(String deviceId) {
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(deviceId).build());
        List<Page<Account>> collect = deviceIdIndex.query(queryConditional).stream().collect(Collectors.toList());
        return collect.get(0).items();
    }

    public void updateDeviceId(Account account) {
        if (StringUtils.isEmpty(account.getDeviceId())) {
            return;
        }
        super.updateIgnoreNulls(account, update -> update.setDeviceId(account.getDeviceId()));
    }

    public void updateLastLogin(Account account) {
        super.updateIgnoreNulls(account, update -> {
            update.setLastLoginUserId(account.getLastLoginUserId());
            update.setLastLoginTime(account.getLastLoginTime());
            update.setChannelId(account.getChannelId());
            update.setDeviceId(account.getDeviceId());
            update.setServerUserIdMap(account.getServerUserIdMap());
        });
    }

}
