package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.Hero;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 14:53
 */
@Repository
public class HeroDao extends BaseDynamoDBDao<Hero> {

    public Hero getByRowId(long userId, long rowId) {
        return super.getItem(userId, rowId);
    }

    public List<Hero> getAllByUserId(long userId) {
        return super.getAll(userId);
    }

    public Hero getByHeroId(long userId, int heroId) {
        Expression expression = Expression.builder()
                .expression("#heroId = :heroId")
                .putExpressionName("#heroId", "heroId")
                .putExpressionValue(":heroId", AttributeValue.fromN(String.valueOf(heroId)))
                .build();
        return super.queryOne(userId, expression);
    }

    @SuppressWarnings("unchecked")
    public List<Hero> getListByRowIds(long userId, List<Long> rowIds) {
        return batchGetItem(userId, rowIds);
    }
}
