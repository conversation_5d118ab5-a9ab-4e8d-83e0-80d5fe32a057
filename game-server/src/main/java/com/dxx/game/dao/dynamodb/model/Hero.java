package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/1 14:52
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("hero")
public class Hero extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId;            // 用户ID
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private Long rowId;             // 唯一ID
    private Integer heroId;         // 配置ID
    private Integer level;          // 等级
    private Integer exp;            // 经验
    private Integer star;           // 星级
    private Integer quality;        // 品质
    private Integer advance;        // 等阶
    private Integer power;          // 英雄战力
    private Integer skinConfigId;   // 穿戴的皮肤 无:0

    /** 装备 */
    private Map<Integer, Equip> equips;


    @DynamoDbIgnore
    @Override
    public Object getUniqueKey() {
        return userId + "_" + rowId;
    }
}
