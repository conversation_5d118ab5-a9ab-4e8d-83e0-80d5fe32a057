package com.dxx.game.dao.redis;

import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.RedisKeys;
import com.dxx.game.dao.dynamodb.model.gameplay.ExpeditionImage;
import com.dxx.game.dao.dynamodb.repository.gameplay.ExpeditionImageDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * 战斗数据
 */
@Slf4j
@Repository
public class ExpeditionRedisDao {
    @Autowired
    private RedisService redisService;

    @Autowired
    private ExpeditionImageDao expeditionImageDao;

    @Resource
    private RedisLock redisLock;


    public void updateMatchList(int warZoneId, int layer, int max, long userId, long power, long time) {
        String key = getMatchKey(warZoneId, layer);
        redisService.listLAdd(key, userId+"|"+power+"|"+time);
        clearMatchList(warZoneId, layer, max);
    }

    public void delMatchKey(int warZoneId, int layer, long userId, long power, long time) {
        String key = getMatchKey(warZoneId, layer);
        redisService.listRemove(key,  userId+"|"+power+"|"+time);
    }

    public void delMatchList(int warZoneId, int layer) {
        String key = getMatchKey(warZoneId, layer);
        redisService.deleteKey(key);
    }

    @Async
    public void clearMatchList(int warZoneId, int layer, int max) {
        int expireInMill = 5000;
        String zsetKey = getMatchKey(warZoneId, layer);
        String key = zsetKey + "clear";
        String lockValue = String.valueOf(System.currentTimeMillis());
        try {
            if (redisLock.lockWithOutRetry(key, lockValue, expireInMill)) {
                var count = redisService.listCount(zsetKey);
                int doCount = 0;
                while (count > max || doCount > 5) {
                    var imageKey = redisService.listRPop(key);
                    String[] split = imageKey.split("\\|");
                    long userId = Long.parseLong(split[0]);
                    long power = Long.parseLong(split[1]);
                    long time = Long.parseLong(split[2]);
                    ExpeditionImage image = new ExpeditionImage();
                    image.setPk(expeditionImageDao.getPK(warZoneId, layer));
                    image.setSk(expeditionImageDao.getSK(userId, time));
                    image.setTtlTime(DateUtils.getUnixTime() + DateUtils.SECONDS_PRE_DAY);
                    expeditionImageDao.updateTTL(image);
                    count = redisService.listCount(zsetKey);
                    doCount++;
                }
            }
        } catch (Exception e) {
            log.error("clearMatchList error", e);

        } finally {
            redisLock.unlock(key, lockValue);
        }
    }

    public List<List<Long>> getMatchList(int warZoneId, int layer, int max) {
        List<String> l = redisService.listRange(getMatchKey(warZoneId, layer), 0, max);
        List<List<Long>> list = new ArrayList<>();
        for (String key : l) {
            String[] split = key.split("\\|");
            long userId = Long.parseLong(split[0]);
            long power = Long.parseLong(split[1]);
            long time = Long.parseLong(split[2]);
            list.add(Arrays.asList(userId, power, time));

        }
        return list;
    }

    private String getMatchKey(int warZoneId, int layer) {
        return String.format(RedisKeys.EXPEDITION_MATCH_KEY, warZoneId, layer);
    }
}
