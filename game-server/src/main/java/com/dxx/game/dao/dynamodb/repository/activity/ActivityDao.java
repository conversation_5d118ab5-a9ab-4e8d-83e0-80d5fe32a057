package com.dxx.game.dao.dynamodb.repository.activity;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBTransactionAspectSupport;
import com.dxx.game.common.aws.dynamodb.utils.DynamoDBConvertUtil;
import com.dxx.game.common.utils.ReflectionUtils;
import com.dxx.game.dao.dynamodb.model.activity.Activity;
import com.dxx.game.dao.dynamodb.model.activity.ActivityBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.QueryRequest;
import software.amazon.awssdk.services.dynamodb.model.QueryResponse;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/3/15
 * @description:
 */
@Slf4j
@Repository
public class ActivityDao extends BaseDynamoDBDao<ActivityBase> {

    public Activity getByUserId(long userId) {
        Activity activity = DynamoDBCacheManager.get(Activity.class, userId);
        if (activity == null) {
            activity = new Activity();
            activity.setUserId(userId);

            QueryRequest queryRequest = QueryRequest.builder()
                    .tableName(this.tableName)
                    .keyConditionExpression("#userId = :userId")
                    .expressionAttributeNames(Map.of("#userId", "userId"))
                    .expressionAttributeValues(Map.of(":userId", DynamoDBConvertUtil.buildAttributeValue(userId)))
                    .consistentRead(true)
                    .build();

            QueryResponse queryResponse = super.dynamoDbClient.query(queryRequest);
            List<Map<String, AttributeValue>> items = queryResponse.items();
            for (Map<String, AttributeValue> item : items) {
                if (!item.containsKey("sk") || !item.containsKey("className")) {
                    log.error("module not found ----> item:{}", item);
                    continue;
                }
                String module = item.get("sk").s();
                Field field = ReflectionUtils.findField(activity.getClass(), module);
                if (field == null) {
                    log.error("field not found ----> module:{}", module);
                    continue;
                }

                String className = item.get("className").s();
                var tableMetadataInfo = DynamoDBTransactionAspectSupport.getTableInfoMap().get(className);
                var newItemObject = tableMetadataInfo.getTableSchema().mapToItem(item);
                ReflectionUtils.setFieldValue(activity, field, newItemObject);

                DynamoDBCacheManager.put(newItemObject);
            }
            DynamoDBCacheManager.put(activity);
        }
        return activity;
    }
}
