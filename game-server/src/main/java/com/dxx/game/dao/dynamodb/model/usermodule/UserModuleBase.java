package com.dxx.game.dao.dynamodb.model.usermodule;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Getter;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

/**
 * @author: lsc
 * @createDate: 2025/3/17
 * @description:
 */
@DynamoDBTableName("user-module")
@DynamoDbBean
@Setter
@Getter
public class UserModuleBase extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    protected Long userId;            // 用户ID
    @Getter(onMethod_ = {@DynamoDbSortKey})
    protected String module;
    protected String className;

    @DynamoDbIgnore
    @Override
    public Object getUniqueKey() {
        return userId + "_" + module;
    }

    public UserModuleBase() {
        this.module = this.getClass().getSimpleName();
        this.className = this.getClass().getName();
    }

    public UserModuleBase(String module) {
        this.module = module;
        this.className = this.getClass().getName();
    }
}
