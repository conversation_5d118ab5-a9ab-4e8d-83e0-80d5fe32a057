package com.dxx.game.dao.dynamodb.repository.activity;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.activity.*;
import org.springframework.stereotype.Repository;

/**
 * @author: lsc
 * @createDate: 2025/3/15
 * @description:
 */
@Repository
public abstract class ActivityBaseDao<T extends ActivityBase> extends BaseDynamoDBDao<T> {

    public T getByUserId(long userId) {
        return super.getItem(userId, sortKey());
    }

    protected String sortKey() {
        return getType().getSimpleName();
    }

    protected abstract Class<T> getType();


    @Repository
    public static class SevenDayTaskDao extends ActivityBaseDao<SevenDayTask> {

        @Override
        protected Class<SevenDayTask> getType() {
            return SevenDayTask.class;
        }
    }

    @Repository
    public static class SignInDao extends ActivityBaseDao<SignIn> {

        @Override
        protected Class<SignIn> getType() {
            return SignIn.class;
        }
    }


    @Repository
    public static class DiveDao extends ActivityBaseDao<Dive> {

        @Override
        protected Class<Dive> getType() {
            return Dive.class;
        }
    }

    @Repository
    public static class FishingDao extends ActivityBaseDao<Fishing> {

        @Override
        protected Class<Fishing> getType() {
            return Fishing.class;
        }
    }

    @Repository
    public static class FlipDao extends ActivityBaseDao<Flip> {

        @Override
        protected Class<Flip> getType() {
            return Flip.class;
        }
    }

    @Repository
    public static class PowerDao extends ActivityBaseDao<Power> {

        @Override
        protected Class<Power> getType() {
            return Power.class;
        }
    }

    @Repository
    public static class EventsDao extends ActivityBaseDao<ActivityCommon> {

        @Override
        protected Class<ActivityCommon> getType() {
            return ActivityCommon.class;
        }
    }
}
