package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/3 18:46
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("task")
public class Task extends DynamoDBBaseModel {
    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId;
    private Map<Integer, TaskModel> dailyTasks;
    private Long dailyTime;
    private Long dailyReward;
    private Short dailyActive;
    private Map<Integer, TaskModel> weeklyTasks;
    private Long weeklyTime;
    private Long weeklyReward;
    private Short weeklyActive;
    private Map<Integer, TaskModel> achievements;

    @DynamoDbIgnore
    @Override
    public Long getUniqueKey() {
        return this.userId;
    }

    @Data
    @DynamoDbBean
    public static class TaskModel {
        /**
         * 类型
         *
         * @see com.dxx.game.consts.TaskType
         */
        private int type;
        // 进度
        private long process;
        // 是否完成
        private int finish;
        // 是否领取奖励
        private int receive;
        /**
         * 大任务类型
         *
         * @see com.dxx.game.consts.TaskType
         */
        private int taskType;

        public static TaskModel valueOf(int type) {
            TaskModel model = new TaskModel();
            model.type = type;
            model.process = 0;
            model.finish = 0;
            model.receive = 0;
            return model;
        }
    }

}
