package com.dxx.game.dao.dynamodb.model.usermodule;

import lombok.Data;
import lombok.Getter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/3/17
 * @description:
 */
@Data
@DynamoDbBean
public class UserExtend extends UserModuleBase {

    private Integer chapterId;                          // 当前主线章节进度
    private Map<Integer, AdInfo> adInfos;               // 广告数据
    private Long lastAdTs;                              // 最后一次看广告时间
    private List<Integer> openModelId;                  // 已开启的模块
    private List<Integer> heroRecords;                  // 英雄获取记录客户端图鉴使用

    /** 英雄增加图鉴积分次数 heroRowId -> count */
    private Map<Integer, Integer> heroScoreCounts;
    /** 英雄增加图鉴积分次数 heroRowId -> count */
    private Map<Integer, Integer> heroBondCounts;
    /** 英雄图鉴 奖励记录 */
    private List<Integer> heroBookRewardRecords;
    private Map<Integer, List<Long>> formations;

    /** 公会科技等级 */
    private Integer guildTechLv;

    /** 获得的皮肤 */
    private List<Integer> skins;

    /** 英雄的一些统计信息*/
    private HeroStatisticModel heroStatisticModel;


    @DynamoDbIgnore
    public static UserExtend init(long userId, int chapterId) {
        UserExtend userExtend = new UserExtend();
        userExtend.setUserId(userId);
        userExtend.setChapterId(chapterId);
        userExtend.setAdInfos(new HashMap<>());
        userExtend.setOpenModelId(new ArrayList<>());
        userExtend.setGuildTechLv(0);
        return userExtend;
    }


    @Data
    @DynamoDbBean
    public static class HeroStatisticModel {
        /** 所有英雄总星数 */
        private Integer totalHeroStars;

        /** 英雄的最高星级*/
        private Integer maxHeroStar;
    }


    @Data
    @DynamoDbBean
    public static class AdInfo {
        private int adId;
        private int count;
        private long rTS;       // 整体次数刷新时间
        private long cdTS;      // 冷却时间
    }
}
