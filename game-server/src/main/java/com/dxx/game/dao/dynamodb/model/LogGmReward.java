package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

/**
 * <AUTHOR>
 * @date 2021/4/29 11:24
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("log-gm-reward")
public class LogGmReward extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId;            // 用户ID
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private String uniqueId;        // 唯一ID
    private Long timestamp;         // 时间戳

    @DynamoDbIgnore
    @Override
    public String getUniqueKey() {
        return userId + "_" + uniqueId;
    }

}
