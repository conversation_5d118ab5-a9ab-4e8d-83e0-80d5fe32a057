package com.dxx.game.dao.dynamodb.model.guild;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.*;

/**
 * @authoer: lsc
 * @createDate: 2023/3/24
 * @description:
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("guild")
public class Guild extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private String PK;                      // 主键
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private String SK;                      // 排序键

    @Getter(onMethod_ = {@DynamoDbSecondaryPartitionKey(indexNames = "guildId-index")})
    private Long guildId;
    @Getter(onMethod_ = {@DynamoDbSecondarySortKey(indexNames = "guildId-index")})
    private String SK2;
    @Getter(onMethod_ = {@DynamoDbSecondaryPartitionKey(indexNames = "guildName-index")})
    private String guildName;                 // 公会名称
    private String guildIntro;                // 公会简介
    private Integer guildIcon;                // 公会图标
    private Integer guildIconBg;              // 公会图标背景
    private Integer guildApplyType;            // 申请类型 (0自由加入, 1审批加入)
    private Integer guildApplyCondition;       // 申请加入条件 (战力等)
    private Integer guildMembersCount;         // 成员数量
    private Integer guildMaxMembersCount;      // 最大成员数量
    private Integer guildLevel;                // 公会等级
    private Integer guildExp;                  // 公会经验
    private Long guildCreateTime;              // 创建时间
    private Long guildUpdateTime;              // 更新时间
    private Integer guildActive;               // 公会活跃度
    private Integer guildDayActive;            // 公会每日活跃度
    private Long guildDayActiveTime;           // 公会每日活跃度刷新时间
    private Integer guildIsDissolved;          // 是否已解散(0 正常, 1解散)
    private Integer guildApplyJoinedCount;     // 申请加入公会人数
    private Integer guildLanguage;             // 语言条件
    private Integer guildVicePresidentCount;   // 副会长数量
    private String guildNotice;                // 公会公告
    private Integer guildManagerCount;         // 公会管理者人数
    private Long guildPresidentUserId;         // 公会会长用户ID
    private Integer guildBossOpenId;           // 开启的公会boss配置表ID
    private Long guildBossTime;                // 公会boss下次刷新时间
    private Integer serverId;                  // 公会serverId

    @DynamoDbIgnore
    @Override
    public String getUniqueKey() {
        return this.PK + "_" + this.SK;
    }
}
