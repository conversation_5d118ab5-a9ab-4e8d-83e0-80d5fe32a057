package com.dxx.game.dao.dynamodb.repository.guild;


import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.redis.UserInfoRedisDao;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.services.dynamodb.model.BatchGetItemRequest;
import software.amazon.awssdk.services.dynamodb.model.BatchGetItemResponse;
import software.amazon.awssdk.services.dynamodb.model.KeysAndAttributes;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @authoer: lsc
 * @createDate: 2023/3/24
 * @description:
 */
@Repository
public class GuildUserDao extends BaseDynamoDBDao<GuildUser> {


    DynamoDbIndex<GuildUser> guildIdIndex;

    @Resource
    private UserInfoRedisDao userInfoRedisDao;

    @PostConstruct
    private void init() {
        guildIdIndex = this.mappedTable.index("guildId-index");
    }

    private String getPK(long userId) {
        return GuildTableKeyPrefix.User + userId;
    }
    private String getSK() {
        return GuildTableKeyPrefix.UserMetaData;
    }

    // 添加主键
    public void setPrimaryKey(GuildUser guildUser) {
        guildUser.setPK(this.getPK(guildUser.getUserId()));
        guildUser.setSK(this.getSK());
        guildUser.setSK2(GuildTableKeyPrefix.UserMetaData);
    }

    // 根据公会ID获取数据
    public List<GuildUser> getAllByGuildId(long guildId) {
        QueryConditional queryConditional
                = QueryConditional.keyEqualTo(Key.builder().partitionValue(guildId).sortValue(GuildTableKeyPrefix.UserMetaData).build());
        List<Page<GuildUser>> collect = guildIdIndex.query(queryConditional).stream().collect(Collectors.toList());
        return collect.get(0).items();
    }

    // 根据用户ID获取数据
    public GuildUser getByUserId(long userId) {
        return super.getItem(this.getPK(userId), this.getSK());
    }

    // 根据用户ID获取数据
    public List<GuildUser> getListByUserIds(List<Long> userIds) {
        List<List<Object>> keys = new ArrayList<>(userIds.size());
        for (Long userId : userIds) {
            keys.add(Lists.newArrayList(this.getPK(userId), this.getSK()));
        }
        return super.batchGetItemWithSortKeys(keys);
    }

    // 插入数据
    public void insert(GuildUser guildUser) {
        userInfoRedisDao.updateGuildId(guildUser.getUserId(), guildUser.getGuildId());
        super.insert(guildUser);
    }

    // 更新公会ID
    public void updateGuildId(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setGuildId(guildUser.getGuildId());
        userInfoRedisDao.updateGuildId(guildUser.getUserId(), guildUser.getGuildId());
        super.updateIgnoreNulls(update);
    }

    // 更新职位
    public void updatePosition(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setPosition(guildUser.getPosition());
        super.updateIgnoreNulls(update);
    }

    // 更新加入时间
    public void updateJoinTime(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setJoinTime(guildUser.getJoinTime());
        super.updateIgnoreNulls(update);
    }

    // 更新申请时间
    public void updateApplyTime(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setApplyTime(guildUser.getApplyTime());
        super.updateIgnoreNulls(update);
    }

    // 更新申请公会id
    public void updateApplyGuildId(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setApplyGuildIds(guildUser.getApplyGuildIds());
        super.updateIgnoreNulls(update);
    }

    public void updateApplyGuildIdNow(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setApplyGuildIds(guildUser.getApplyGuildIds());
        super.updateNowIgnoreNulls(update);
    }

    // 更新公会任务数据
    public void updateGuildTask(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setTasks(guildUser.getTasks());
        super.updateIgnoreNulls(update);
    }

    // 更新任务刷新次数
    public void updateGuildTaskRefreshCount(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setTaskRefreshCount(guildUser.getTaskRefreshCount());
        super.updateIgnoreNulls(update);
    }

    // 更新每日商店
    public void updateDailyShop(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setDailyShop(guildUser.getDailyShop());
        super.updateIgnoreNulls(update);
    }

    // 更新每周商店
    public void updateWeeklyShop(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setWeeklyShop(guildUser.getWeeklyShop());
        super.updateIgnoreNulls(update);
    }

    // 更新签到次数
    public void updateSignIn(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setSignInCnt(guildUser.getSignInCnt());
        super.updateIgnoreNulls(update);
    }

    // 更新所有活跃度
    public void updateAllActive(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setActive(guildUser.getActive());
        update.setDailyActive(guildUser.getDailyActive());
        update.setWeeklyActive(guildUser.getWeeklyActive());
        super.updateIgnoreNulls(update);
    }

    // 更新总活跃度
    public void updateActive(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setActive(guildUser.getActive());
        super.updateIgnoreNulls(update);
    }

    // 更新每日活跃度
    public void updateDailyActive(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setDailyActive(guildUser.getDailyActive());
        super.updateIgnoreNulls(update);
    }

    // 更新每周活跃度
    public void updateWeeklyActive(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setWeeklyActive(guildUser.getWeeklyActive());
        super.updateIgnoreNulls(update);
    }

    // 更新每日数据刷新时间
    public void updateDailyTM(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setDailyTM(guildUser.getDailyTM());
        super.updateIgnoreNulls(update);
    }

    // 更新每周数据刷新时间
    public void updateWeeklyTM(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setWeeklyTM(guildUser.getWeeklyTM());
        super.updateIgnoreNulls(update);
    }

    // 更新公会等级记录
    public void updateGuildLevelRecord(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setGuildLevelRecord(guildUser.getGuildLevelRecord());
        super.updateIgnoreNulls(update);
    }

    // 更新被踢出公会信息
    public void updateBeKickedOutInfo(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setBeKickedOutInfo(guildUser.getBeKickedOutInfo());
        super.updateIgnoreNulls(update);
    }

    // 更新公会boss数据
    public void updateGuildBossModel(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setGuildBossModel(guildUser.getGuildBossModel());
        super.updateIgnoreNulls(update);
    }

    public void updateDonation(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setDonationItemCount(guildUser.getDonationItemCount());
        update.setReqItemTM(guildUser.getReqItemTM());
        super.updateIgnoreNulls(update);
    }
}
