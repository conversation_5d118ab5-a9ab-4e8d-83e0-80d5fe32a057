// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: chat.proto

package com.dxx.game.dto;

public final class ChatProto {
  private ChatProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ChatShowItemRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Chat.ChatShowItemRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 物品类型(道具表类型)
     * </pre>
     *
     * <code>uint32 itemType = 2;</code>
     * @return The itemType.
     */
    int getItemType();

    /**
     * <pre>
     * 物品唯一ID
     * </pre>
     *
     * <code>uint64 rowId = 3;</code>
     * @return The rowId.
     */
    long getRowId();

    /**
     * <pre>
     * 群组ID
     * </pre>
     *
     * <code>string groupId = 4;</code>
     * @return The groupId.
     */
    java.lang.String getGroupId();
    /**
     * <pre>
     * 群组ID
     * </pre>
     *
     * <code>string groupId = 4;</code>
     * @return The bytes for groupId.
     */
    com.google.protobuf.ByteString
        getGroupIdBytes();
  }
  /**
   * <pre>
   *CMD PackageId=32105 聊天-展示物品
   * </pre>
   *
   * Protobuf type {@code Proto.Chat.ChatShowItemRequest}
   */
  public static final class ChatShowItemRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Chat.ChatShowItemRequest)
      ChatShowItemRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChatShowItemRequest.newBuilder() to construct.
    private ChatShowItemRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChatShowItemRequest() {
      groupId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChatShowItemRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChatShowItemRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              itemType_ = input.readUInt32();
              break;
            }
            case 24: {

              rowId_ = input.readUInt64();
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              groupId_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ChatProto.internal_static_Proto_Chat_ChatShowItemRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ChatProto.internal_static_Proto_Chat_ChatShowItemRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ChatProto.ChatShowItemRequest.class, com.dxx.game.dto.ChatProto.ChatShowItemRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int ITEMTYPE_FIELD_NUMBER = 2;
    private int itemType_;
    /**
     * <pre>
     * 物品类型(道具表类型)
     * </pre>
     *
     * <code>uint32 itemType = 2;</code>
     * @return The itemType.
     */
    @java.lang.Override
    public int getItemType() {
      return itemType_;
    }

    public static final int ROWID_FIELD_NUMBER = 3;
    private long rowId_;
    /**
     * <pre>
     * 物品唯一ID
     * </pre>
     *
     * <code>uint64 rowId = 3;</code>
     * @return The rowId.
     */
    @java.lang.Override
    public long getRowId() {
      return rowId_;
    }

    public static final int GROUPID_FIELD_NUMBER = 4;
    private volatile java.lang.Object groupId_;
    /**
     * <pre>
     * 群组ID
     * </pre>
     *
     * <code>string groupId = 4;</code>
     * @return The groupId.
     */
    @java.lang.Override
    public java.lang.String getGroupId() {
      java.lang.Object ref = groupId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        groupId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 群组ID
     * </pre>
     *
     * <code>string groupId = 4;</code>
     * @return The bytes for groupId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getGroupIdBytes() {
      java.lang.Object ref = groupId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        groupId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (itemType_ != 0) {
        output.writeUInt32(2, itemType_);
      }
      if (rowId_ != 0L) {
        output.writeUInt64(3, rowId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(groupId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, groupId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (itemType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, itemType_);
      }
      if (rowId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, rowId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(groupId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, groupId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ChatProto.ChatShowItemRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ChatProto.ChatShowItemRequest other = (com.dxx.game.dto.ChatProto.ChatShowItemRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getItemType()
          != other.getItemType()) return false;
      if (getRowId()
          != other.getRowId()) return false;
      if (!getGroupId()
          .equals(other.getGroupId())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + ITEMTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getItemType();
      hash = (37 * hash) + ROWID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRowId());
      hash = (37 * hash) + GROUPID_FIELD_NUMBER;
      hash = (53 * hash) + getGroupId().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ChatProto.ChatShowItemRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ChatProto.ChatShowItemRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=32105 聊天-展示物品
     * </pre>
     *
     * Protobuf type {@code Proto.Chat.ChatShowItemRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Chat.ChatShowItemRequest)
        com.dxx.game.dto.ChatProto.ChatShowItemRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ChatProto.internal_static_Proto_Chat_ChatShowItemRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ChatProto.internal_static_Proto_Chat_ChatShowItemRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ChatProto.ChatShowItemRequest.class, com.dxx.game.dto.ChatProto.ChatShowItemRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.ChatProto.ChatShowItemRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        itemType_ = 0;

        rowId_ = 0L;

        groupId_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ChatProto.internal_static_Proto_Chat_ChatShowItemRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ChatProto.ChatShowItemRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.ChatProto.ChatShowItemRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ChatProto.ChatShowItemRequest build() {
        com.dxx.game.dto.ChatProto.ChatShowItemRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ChatProto.ChatShowItemRequest buildPartial() {
        com.dxx.game.dto.ChatProto.ChatShowItemRequest result = new com.dxx.game.dto.ChatProto.ChatShowItemRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.itemType_ = itemType_;
        result.rowId_ = rowId_;
        result.groupId_ = groupId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ChatProto.ChatShowItemRequest) {
          return mergeFrom((com.dxx.game.dto.ChatProto.ChatShowItemRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ChatProto.ChatShowItemRequest other) {
        if (other == com.dxx.game.dto.ChatProto.ChatShowItemRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getItemType() != 0) {
          setItemType(other.getItemType());
        }
        if (other.getRowId() != 0L) {
          setRowId(other.getRowId());
        }
        if (!other.getGroupId().isEmpty()) {
          groupId_ = other.groupId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.ChatProto.ChatShowItemRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.ChatProto.ChatShowItemRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int itemType_ ;
      /**
       * <pre>
       * 物品类型(道具表类型)
       * </pre>
       *
       * <code>uint32 itemType = 2;</code>
       * @return The itemType.
       */
      @java.lang.Override
      public int getItemType() {
        return itemType_;
      }
      /**
       * <pre>
       * 物品类型(道具表类型)
       * </pre>
       *
       * <code>uint32 itemType = 2;</code>
       * @param value The itemType to set.
       * @return This builder for chaining.
       */
      public Builder setItemType(int value) {
        
        itemType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 物品类型(道具表类型)
       * </pre>
       *
       * <code>uint32 itemType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemType() {
        
        itemType_ = 0;
        onChanged();
        return this;
      }

      private long rowId_ ;
      /**
       * <pre>
       * 物品唯一ID
       * </pre>
       *
       * <code>uint64 rowId = 3;</code>
       * @return The rowId.
       */
      @java.lang.Override
      public long getRowId() {
        return rowId_;
      }
      /**
       * <pre>
       * 物品唯一ID
       * </pre>
       *
       * <code>uint64 rowId = 3;</code>
       * @param value The rowId to set.
       * @return This builder for chaining.
       */
      public Builder setRowId(long value) {
        
        rowId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 物品唯一ID
       * </pre>
       *
       * <code>uint64 rowId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowId() {
        
        rowId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object groupId_ = "";
      /**
       * <pre>
       * 群组ID
       * </pre>
       *
       * <code>string groupId = 4;</code>
       * @return The groupId.
       */
      public java.lang.String getGroupId() {
        java.lang.Object ref = groupId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          groupId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 群组ID
       * </pre>
       *
       * <code>string groupId = 4;</code>
       * @return The bytes for groupId.
       */
      public com.google.protobuf.ByteString
          getGroupIdBytes() {
        java.lang.Object ref = groupId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          groupId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 群组ID
       * </pre>
       *
       * <code>string groupId = 4;</code>
       * @param value The groupId to set.
       * @return This builder for chaining.
       */
      public Builder setGroupId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        groupId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群组ID
       * </pre>
       *
       * <code>string groupId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearGroupId() {
        
        groupId_ = getDefaultInstance().getGroupId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群组ID
       * </pre>
       *
       * <code>string groupId = 4;</code>
       * @param value The bytes for groupId to set.
       * @return This builder for chaining.
       */
      public Builder setGroupIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        groupId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Chat.ChatShowItemRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Chat.ChatShowItemRequest)
    private static final com.dxx.game.dto.ChatProto.ChatShowItemRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ChatProto.ChatShowItemRequest();
    }

    public static com.dxx.game.dto.ChatProto.ChatShowItemRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChatShowItemRequest>
        PARSER = new com.google.protobuf.AbstractParser<ChatShowItemRequest>() {
      @java.lang.Override
      public ChatShowItemRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChatShowItemRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChatShowItemRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChatShowItemRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ChatProto.ChatShowItemRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChatShowItemResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Chat.ChatShowItemResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=32106
   * </pre>
   *
   * Protobuf type {@code Proto.Chat.ChatShowItemResponse}
   */
  public static final class ChatShowItemResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Chat.ChatShowItemResponse)
      ChatShowItemResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChatShowItemResponse.newBuilder() to construct.
    private ChatShowItemResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChatShowItemResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChatShowItemResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChatShowItemResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ChatProto.internal_static_Proto_Chat_ChatShowItemResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ChatProto.internal_static_Proto_Chat_ChatShowItemResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ChatProto.ChatShowItemResponse.class, com.dxx.game.dto.ChatProto.ChatShowItemResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ChatProto.ChatShowItemResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ChatProto.ChatShowItemResponse other = (com.dxx.game.dto.ChatProto.ChatShowItemResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ChatProto.ChatShowItemResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChatProto.ChatShowItemResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ChatProto.ChatShowItemResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=32106
     * </pre>
     *
     * Protobuf type {@code Proto.Chat.ChatShowItemResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Chat.ChatShowItemResponse)
        com.dxx.game.dto.ChatProto.ChatShowItemResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ChatProto.internal_static_Proto_Chat_ChatShowItemResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ChatProto.internal_static_Proto_Chat_ChatShowItemResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ChatProto.ChatShowItemResponse.class, com.dxx.game.dto.ChatProto.ChatShowItemResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.ChatProto.ChatShowItemResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ChatProto.internal_static_Proto_Chat_ChatShowItemResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ChatProto.ChatShowItemResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.ChatProto.ChatShowItemResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ChatProto.ChatShowItemResponse build() {
        com.dxx.game.dto.ChatProto.ChatShowItemResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ChatProto.ChatShowItemResponse buildPartial() {
        com.dxx.game.dto.ChatProto.ChatShowItemResponse result = new com.dxx.game.dto.ChatProto.ChatShowItemResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ChatProto.ChatShowItemResponse) {
          return mergeFrom((com.dxx.game.dto.ChatProto.ChatShowItemResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ChatProto.ChatShowItemResponse other) {
        if (other == com.dxx.game.dto.ChatProto.ChatShowItemResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.ChatProto.ChatShowItemResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.ChatProto.ChatShowItemResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Chat.ChatShowItemResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Chat.ChatShowItemResponse)
    private static final com.dxx.game.dto.ChatProto.ChatShowItemResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ChatProto.ChatShowItemResponse();
    }

    public static com.dxx.game.dto.ChatProto.ChatShowItemResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChatShowItemResponse>
        PARSER = new com.google.protobuf.AbstractParser<ChatShowItemResponse>() {
      @java.lang.Override
      public ChatShowItemResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChatShowItemResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChatShowItemResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChatShowItemResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ChatProto.ChatShowItemResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Chat_ChatShowItemRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Chat_ChatShowItemRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Chat_ChatShowItemResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Chat_ChatShowItemResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\nchat.proto\022\nProto.Chat\032\014common.proto\"y" +
      "\n\023ChatShowItemRequest\0220\n\014commonParams\030\001 " +
      "\001(\0132\032.Proto.Common.CommonParams\022\020\n\010itemT" +
      "ype\030\002 \001(\r\022\r\n\005rowId\030\003 \001(\004\022\017\n\007groupId\030\004 \001(" +
      "\t\"R\n\024ChatShowItemResponse\022\014\n\004code\030\001 \001(\005\022" +
      ",\n\ncommonData\030\002 \001(\0132\030.Proto.Common.Commo" +
      "nDataB\035\n\020com.dxx.game.dtoB\tChatProtob\006pr" +
      "oto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Chat_ChatShowItemRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Chat_ChatShowItemRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Chat_ChatShowItemRequest_descriptor,
        new java.lang.String[] { "CommonParams", "ItemType", "RowId", "GroupId", });
    internal_static_Proto_Chat_ChatShowItemResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Chat_ChatShowItemResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Chat_ChatShowItemResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", });
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
