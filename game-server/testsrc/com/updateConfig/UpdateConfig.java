package com.updateConfig;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.dxx.game.GameServerApplication;
import com.dxx.game.common.config.game.GameConfigSupport;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.OpenOption;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

/**
 * @Description : TODO
 * <AUTHOR> wzy
 * @Date : 2022/3/9 20:43
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {GameServerApplication.class})
@TestPropertySource(locations = {"classpath:application.properties"})
@Slf4j
public class UpdateConfig {

    @Autowired
    private GameConfigSupport configSupport;

    @Test
    public void updateConfig() {

        String url = "http://config.gorilla10.com/check/getFileList?gameId=20&key=a5f0a9b3c9e1c7e0";
        String fileIds = OkHttpClientUtil.get(url, String.class);


//        String fileIds="111,306,110,195,112,303,302,108,113,120,118,200,124,117,115,122,109,114,116,119,123,121,125";

        JSONObject params = new JSONObject();
        params.put("key", "933080a605eee557");
        params.put("fileIds", fileIds);
        configSupport.syncConfig(params);

        try {
            File directory = new File("");
            String appPath = directory.getCanonicalPath();
            String itemPath = appPath + "/gameconf/Item.json";
            File file = new File(itemPath);
            if (file.exists()) {
                Stream<String> lines = Files.lines(Paths.get(itemPath));
                StringBuilder sb = new StringBuilder();
                lines.forEach(line -> {
                    sb.append(line);
                });
                JSONObject jsonObject = JSONObject.parseObject(sb.toString(), Feature.OrderedField);
                boolean item = jsonObject.containsKey("Item");
                JSONObject item1 = jsonObject.getJSONObject("Item");
                String s = item1.toString();
                System.out.println(s);
                String wikiItemPath = appPath + "/tools/api/config/item.json";
                File wikiItemFile = new File(wikiItemPath);
                if (!wikiItemFile.exists()) {
                    wikiItemFile.createNewFile();
                }
                PrintStream stream = null;
                stream = new PrintStream(wikiItemPath);
                stream.print(s);
            }
        } catch (Exception e) {
            log.error("updateConfig, e:",e);
        }
    }
}
