var fs = require("fs");
var path = require("path");


var fileName = "";

var args = process.argv.splice(2);
if (args.length == 0) {
	console.log("fileName empty");
	return;
}

fileName = args[0];

var dataPath = path.join(__dirname, "./data");
if (!fs.existsSync(dataPath)) {
	fs.mkdirSync(dataPath);
}
var modifyfilePath = dataPath + "/modifyfile.txt";
var content = fs.readFileSync(modifyfilePath,'utf-8');

var modifyList = JSON.parse(content);

let index = modifyList.indexOf(fileName);

if (index == -1) {
	process.exit(0);
}

process.exit(1);
